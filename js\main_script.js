// js/main_script.js

// Import libraries and utilities
import utils from './utils.js';
// Import chart utilities but not ChartManager (will be loaded dynamically)
import { extractDataFromRange, formatRangeAddress } from './chart-utils.js';
// Import sheet manager for multi-sheet support
import sheetManager from './sheet_manager.js';
// Import file protection module
import FileProtection from './file-protection.js';
import ChartManager from './chart-manager.js'; // Use the default export
import { createModal } from './modal_utils.js'; // Import the new modal utility
import {
    createStandardModal,
    createChartModal,
    createExportModal,
    createLibraryDemoModal,
    createPageSetupModal,
    createWorkbookSettingsModal
} from './standardized-modal.js'; // Import standardized modal utilities
// Import history manager for undo/redo
import historyManager from './history_manager.js';
// Import styler functions for format painter
import { toggleFormatPainter, applyFormatPainter, isFormatPainterActive } from './styler.js';

// Export functions for use in app-init.js
export {
    init,
    handleFileSelect as openFile,
    handleFileSave as saveFile,
    createNewWorkbook,
    showChartDialog,
    handleFileSelect as importFile,
    handlePrint as printSheet
};

// Also expose functions globally for direct access
window.createNewWorkbook = createNewWorkbook;
window.handleFileSelect = handleFileSelect;
window.handleFileSave = handleFileSave;

// Global state
let workbook = null;
let currentSheet = null;
let activeCell = null;
let selectedRange = null;
let defaultFontSize = 11;
let isEditing = false;

// Initialize the application
function init() {
    console.log('Initializing main script...');

    // Initialize Excel editor
    initializeExcelEditor();

    console.log('Main script initialized');
}

// For backward compatibility
document.addEventListener('DOMContentLoaded', () => {
    // This will be called by app-init.js instead
    // initializeExcelEditor();
});

function initializeExcelEditor() {
    // Initialize UI elements
    const fileInput = document.createElement('input');
    fileInput.type = 'file';
    fileInput.accept = '.xlsx,.xls,.envent,.xlsm';
    fileInput.style.display = 'none';
    fileInput.id = 'fileInput';
    document.body.appendChild(fileInput);
    console.log('Created file input in main_script.js with accept:', fileInput.accept);

    // Setup event listeners
    setupFileHandlers(fileInput);
    setupToolbarHandlers();
    setupFormulaBar();

    // Initialize managers
    initializeManagers();

    // Initialize file protection
    initializeFileProtection();
}

// Initialize the file protection module
function initializeFileProtection() {
    try {
        console.log('Initializing file protection module...');

        // Check if FileProtection is already initialized in app-init.js
        if (window.FileProtection) {
            console.log('Using existing FileProtection instance from app-init.js');
        } else {
            // Create a new instance if not already created
            window.FileProtection = new FileProtection();
            console.log('Created new FileProtection instance');
        }

        // Ensure the lock button has an event listener
        const lockBtn = document.getElementById('lockFileBtn');
        if (lockBtn) {
            console.log('Adding click event listener to lock button');
            lockBtn.addEventListener('click', () => {
                console.log('Lock button clicked');
                if (window.FileProtection && typeof window.FileProtection.showPasswordModal === 'function') {
                    window.FileProtection.showPasswordModal();
                } else {
                    console.error('FileProtection instance or showPasswordModal method not found');
                }
            });
        } else {
            console.warn('Lock button not found in the DOM during initialization');
        }

        console.log('File protection module initialized successfully');
    } catch (error) {
        console.error('Error initializing file protection module:', error);
    }
}

function initializeManagers() {
    // Initialize chart manager
    try {
        ChartManager.init(workbook, currentSheet); // Initialize the singleton
        window.chartManager = ChartManager; // Assign the singleton
        console.log('Chart manager initialized successfully');
    } catch (error) {
        console.error('Error initializing chart manager:', error);
    }

    // Initialize formula manager if available
    if (window.formulaManager) {
        try {
            window.formulaManager.initializeFormulaBar();
            console.log('Formula manager initialized successfully');
        } catch (error) {
            console.error('Error initializing formula manager:', error);
        }
    } else {
        console.log('Formula manager not available, skipping initialization');
    }

    // Log initialization status
    console.log('Managers initialization completed');
}

function setupFileHandlers(fileInput) {
    // File menu handlers
    document.getElementById('newWorkbookBtn').addEventListener('click', createNewWorkbook);

    // Add a global flag to prevent multiple file dialogs
    window._fileDialogOpen = false;

    document.getElementById('openFileBtn').addEventListener('click', () => {
        // Check if a file dialog is already open
        if (window._fileDialogOpen) {
            console.log('File dialog already open, ignoring click');
            return;
        }

        // Set the flag to indicate a file dialog is open
        window._fileDialogOpen = true;

        // Click the file input to open the file dialog
        fileInput.click();
    });

    document.getElementById('saveFileBtn').addEventListener('click', handleFileSave);
    document.getElementById('saveAsFileBtn').addEventListener('click', handleFileSaveAs);
    document.getElementById('exportFileBtn').addEventListener('click', showExportModal);
    document.getElementById('printBtn').addEventListener('click', handlePrint);
    document.getElementById('pageSetupBtn').addEventListener('click', showPageSetupModal);
    document.getElementById('workbookSettingsBtn').addEventListener('click', showWorkbookSettingsModal);

    // Library demo handler
    const libraryDemoBtn = document.getElementById('libraryDemoBtn');
    if (libraryDemoBtn) {
        libraryDemoBtn.addEventListener('click', showLibraryDemo);
    }

    // Add change event listener to the file input
    fileInput.addEventListener('change', (event) => {
        // Reset the file dialog flag
        window._fileDialogOpen = false;

        // Handle the file selection
        handleFileSelect(event);
    });

    // Add cancel event listener to reset the flag when the dialog is canceled
    fileInput.addEventListener('cancel', () => {
        window._fileDialogOpen = false;
    });
}

// Show the library demo
function showLibraryDemo() {
    // Create a standardized library demo modal
    const modalInstance = createLibraryDemoModal();

    // Show the modal
    modalInstance.show();

    // Add event listeners for demo buttons
    document.getElementById('createWorkbookBtn').addEventListener('click', async () => {
        const result = document.getElementById('excelDemoResult');
        result.innerHTML = '<p>Creating workbook...</p>';

        try {
            const workbook = await utils.excel.createBlankWorkbook();
            const sheet = utils.excel.getFirstSheet(workbook);

            // Add some data
            if (utils.excel.activeLibrary === 'xlsx-populate') {
                sheet.cell('A1').value('Name');
                sheet.cell('B1').value('Age');
                sheet.cell('A2').value('John Doe');
                sheet.cell('B2').value(30);
            }

            await utils.excel.saveWorkbook(workbook, 'demo_workbook.xlsx');
            result.innerHTML = '<p>Workbook created and saved as demo_workbook.xlsx</p>';
        } catch (error) {
            result.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    });

    document.getElementById('exportCsvBtn').addEventListener('click', () => {
        const result = document.getElementById('excelDemoResult');
        result.innerHTML = '<p>Exporting to CSV...</p>';

        try {
            const data = [
                ['Name', 'Age', 'City'],
                ['John Doe', 30, 'New York'],
                ['Jane Smith', 25, 'Los Angeles']
            ];

            utils.export.exportToCsv(data, 'demo_export.csv');
            result.innerHTML = '<p>Data exported as demo_export.csv</p>';
        } catch (error) {
            result.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    });

    document.getElementById('createChartBtn').addEventListener('click', () => {
        const result = document.getElementById('chartDemoResult');
        result.innerHTML = '<div id="chartContainer" style="width: 100%; height: 300px;"></div>';

        try {
            const data = {
                categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                values: [30, 40, 35, 50, 49, 60]
            };

            utils.chart.createChart(
                document.getElementById('chartContainer'),
                'bar',
                data,
                { title: 'Monthly Sales' }
            );
        } catch (error) {
            result.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    });

    document.getElementById('createKpiBtn').addEventListener('click', () => {
        const result = document.getElementById('chartDemoResult');
        result.innerHTML = '<div id="kpiContainer" style="width: 200px; height: 200px;"></div>';

        try {
            utils.chart.createKpiCard(
                document.getElementById('kpiContainer'),
                75,
                'Completion'
            );
        } catch (error) {
            result.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    });

    document.getElementById('createGridBtn').addEventListener('click', () => {
        const result = document.getElementById('gridDemoResult');
        result.innerHTML = '<div id="gridContainer"></div>';

        try {
            const data = [
                { name: 'John', age: 30, city: 'New York' },
                { name: 'Jane', age: 25, city: 'Los Angeles' },
                { name: 'Bob', age: 40, city: 'Chicago' }
            ];

            utils.grid.createDataGrid(
                document.getElementById('gridContainer'),
                data
            );
        } catch (error) {
            result.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    });

    document.getElementById('createDashboardBtn').addEventListener('click', () => {
        const result = document.getElementById('gridDemoResult');
        result.innerHTML = '<div id="dashboardContainer" style="height: 400px;"></div>';

        try {
            const dashboard = utils.grid.createDashboard(
                document.getElementById('dashboardContainer')
            );

            utils.grid.addWidget(dashboard, '<div>Widget 1</div>');
            utils.grid.addWidget(dashboard, '<div>Widget 2</div>', { x: 4 });
            utils.grid.addWidget(dashboard, '<div>Widget 3</div>', { x: 8 });
        } catch (error) {
            result.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    });

    document.getElementById('dateFormatBtn').addEventListener('click', () => {
        const result = document.getElementById('utilityDemoResult');

        try {
            const now = new Date();
            const formatted = utils.date.format(now, 'YYYY-MM-DD HH:mm:ss');
            const tomorrow = utils.date.addDays(now, 1);
            const tomorrowFormatted = utils.date.format(tomorrow, 'YYYY-MM-DD');

            result.innerHTML = `
                <p>Current date: ${formatted}</p>
                <p>Tomorrow: ${tomorrowFormatted}</p>
            `;
        } catch (error) {
            result.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    });

    document.getElementById('exportPdfBtn').addEventListener('click', async () => {
        const result = document.getElementById('utilityDemoResult');
        result.innerHTML = '<p>Exporting to PDF...</p>';

        try {
            // Create content to export
            const content = document.createElement('div');
            content.innerHTML = `
                <h1>PDF Export Demo</h1>
                <p>This is a sample PDF export.</p>
                <table>
                    <tr><th>Name</th><th>Age</th></tr>
                    <tr><td>John Doe</td><td>30</td></tr>
                    <tr><td>Jane Smith</td><td>25</td></tr>
                </table>
            `;

            await utils.export.exportToPdf(content, 'demo_export.pdf');
            result.innerHTML = '<p>Content exported as demo_export.pdf</p>';
        } catch (error) {
            result.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    });

    document.getElementById('createTourBtn').addEventListener('click', () => {
        const result = document.getElementById('utilityDemoResult');

        try {
            const tour = utils.ui.createIntroTour([
                {
                    element: document.querySelector('.modal-header'),
                    intro: 'This is the modal header'
                },
                {
                    element: document.getElementById('chartDemoResult'),
                    intro: 'Here you can see chart demos'
                },
                {
                    element: document.getElementById('gridDemoResult'),
                    intro: 'Here you can see grid demos'
                }
            ]);

            tour.start();
            result.innerHTML = '<p>Tour started</p>';
        } catch (error) {
            result.innerHTML = `<p>Error: ${error.message}</p>`;
        }
    });
}

// Create a new empty workbook
async function createNewWorkbook() {
    console.log('Creating new workbook...');
    const toolbar = document.getElementById('toolbar');
    const formulaBar = document.getElementById('formulaBar');
    const spreadsheetContainer = document.getElementById('spreadsheetContainer');
    const sheetTabsContainer = document.getElementById('sheetTabsContainer');

    try {
        if (typeof updateStatus === 'function') {
            updateStatus('Creating new workbook...');
        }

        // Create a new blank workbook using our utility
        workbook = await utils.excel.createBlankWorkbook();
        currentSheet = utils.excel.getFirstSheet(workbook);

        // Set a default filename for new workbooks
        workbook.originalFilename = 'new_spreadsheet.xlsx';

        // Make workbook and currentSheet available globally
        window.workbook = workbook;
        window.currentSheet = currentSheet;

        // Clear any existing content
        if (spreadsheetContainer) {
            spreadsheetContainer.innerHTML = '';
        }

        // Initialize the sheet manager
        sheetManager.initialize(workbook, (sheet, index) => {
            // This callback is called when the active sheet changes
            currentSheet = sheet;
            window.currentSheet = sheet;
            renderSheet();
        });

        // Show toolbar, formula bar, and sheet tabs
        if (toolbar) toolbar.style.display = 'flex';
        if (formulaBar) formulaBar.style.display = 'flex';
        if (sheetTabsContainer) sheetTabsContainer.style.display = 'flex';

        if (typeof updateStatus === 'function') {
            updateStatus('New workbook created successfully');
        }

        return { workbook, currentSheet };
    } catch (error) {
        console.error('Error creating new workbook:', error);

        if (typeof updateStatus === 'function') {
            updateStatus('Error creating new workbook: ' + error.message, 'error');
        }

        // Reset state on error
        workbook = null;
        currentSheet = null;
        window.workbook = null;
        window.currentSheet = null;

        if (spreadsheetContainer) {
            spreadsheetContainer.innerHTML = '<p>Error creating new workbook. Please try again.</p>';
        }

        throw error;
    }
}

function handlePrint() {
    if (!workbook) {
        updateStatus('No workbook to print', 'error');
        return;
    }
    window.print();
}

function setupToolbarHandlers() {
    // Initialize history manager
    historyManager.initialize(renderSheet);

    // Make historyManager available globally
    window.historyManager = historyManager;

    // Undo/Redo
    document.getElementById('undoBtn').addEventListener('click', handleUndo);
    document.getElementById('redoBtn').addEventListener('click', handleRedo);

    // Add keyboard shortcuts for undo/redo
    document.addEventListener('keydown', (e) => {
        // Ctrl+Z for undo
        if (e.ctrlKey && e.key === 'z' && !e.shiftKey) {
            e.preventDefault();
            handleUndo();
        }
        // Ctrl+Y for redo
        else if ((e.ctrlKey && e.key === 'y') || (e.ctrlKey && e.shiftKey && e.key === 'Z')) {
            e.preventDefault();
            handleRedo();
        }
        // Ctrl+Shift+C for format painter
        else if (e.ctrlKey && e.shiftKey && e.key === 'C') {
            e.preventDefault();
            handleFormatPainter();
        }
    });

    // Format Painter
    document.getElementById('formatPainterBtn').addEventListener('click', handleFormatPainter);

    // Merge/Unmerge cells
    document.getElementById('mergeBtn').addEventListener('click', handleMergeCells);
    document.getElementById('unmergeBtn').addEventListener('click', handleUnmergeCells);

    // Text formatting
    document.getElementById('boldBtn').addEventListener('click', () => applyStyle('bold'));
    document.getElementById('italicBtn').addEventListener('click', () => applyStyle('italic'));
    document.getElementById('underlineBtn').addEventListener('click', () => applyStyle('underline'));

    // Colors - Apply immediately to selected cell(s) on input (as user selects color) instead of change
    document.getElementById('fontColorPicker').addEventListener('input', (e) => {
        // Apply to all selected cells
        applyStyle('fontColor', e.target.value);
    });
    document.getElementById('bgColorPicker').addEventListener('input', (e) => {
        // Apply to all selected cells
        applyStyle('fill', e.target.value);
    });

    // Lock button for password protection - REMOVED from here, handled in initializeFileProtection
    // const lockBtn = document.getElementById('lockFileBtn');
    // if (lockBtn) {
    //     console.log('Adding click event listener to lock button in setupToolbarHandlers');
    // lockBtn.addEventListener('click', (e) => {
    //         console.log('Lock button clicked from setupToolbarHandlers');
    // e.preventDefault();
    // e.stopPropagation();
    //         if (window.FileProtection && typeof window.FileProtection.showPasswordModal === 'function') {
    // window.FileProtection.showPasswordModal();
    //         } else {
    //             console.error('FileProtection instance or showPasswordModal method not found');
    //             updateStatus('Password protection feature is not available', 'error');
    //         }
    //     });
    // }

    // Font size
    document.getElementById('decreaseFontSizeBtn').addEventListener('click', () => adjustFontSize(-1));
    document.getElementById('increaseFontSizeBtn').addEventListener('click', () => adjustFontSize(1));

    // Font size dropdown
    const fontSizeSelect = document.getElementById('fontSizeSelect');
    fontSizeSelect.addEventListener('change', (e) => {
        const newSize = parseInt(e.target.value);
        if (!isNaN(newSize) && newSize >= 6 && newSize <= 72) {
            applyFontSizeDirectly(newSize);
        } else {
            // Reset to default if invalid
            e.target.value = defaultFontSize;
        }
    });

    // Update font size dropdown when a cell is selected
    document.addEventListener('cell-selected', (e) => {
        updateFontSizeInput();
    });

    // Number format
    document.getElementById('numberFormatSelect').addEventListener('change', (e) => applyStyle('numberFormat', e.target.value));

    // Font family
    document.getElementById('fontFamilySelect').addEventListener('change', (e) => applyStyle('fontFamily', e.target.value));

    // Alignment
    document.getElementById('alignLeftBtn').addEventListener('click', () => applyStyle('horizontalAlignment', 'left'));
    document.getElementById('alignCenterBtn').addEventListener('click', () => applyStyle('horizontalAlignment', 'center'));
    document.getElementById('alignRightBtn').addEventListener('click', () => applyStyle('horizontalAlignment', 'right'));

    // Row/Column operations
    document.getElementById('insertRowAboveBtn').addEventListener('click', () => insertRow('above'));
    document.getElementById('insertRowBelowBtn').addEventListener('click', () => insertRow('below'));
    document.getElementById('deleteSelectedRowsBtn').addEventListener('click', deleteRows);
    document.getElementById('insertColLeftBtn').addEventListener('click', () => insertColumn('left'));
    document.getElementById('insertColRightBtn').addEventListener('click', () => insertColumn('right'));
    document.getElementById('deleteSelectedColsBtn').addEventListener('click', deleteColumns);

    // Data operations
    document.getElementById('sortAscBtn').addEventListener('click', sortData);
    document.getElementById('filterBtn').addEventListener('click', toggleFilter);
    document.getElementById('formulaBtn').addEventListener('click', toggleFormulaBar);
    document.getElementById('insertChartBtn').addEventListener('click', () => {
        console.log('Insert chart button clicked in main_script.js');

        if (!workbook || !currentSheet) {
            updateStatus('Please open a workbook first', 'error');
            return;
        }

        if (!selectedRange) {
            updateStatus('Please select a range of cells first', 'error');
            return;
        }

        // Ensure Chart Manager is initialized and has the current sheet
        if (window.chartManager && typeof window.chartManager.init === 'function' && typeof window.chartManager.setCurrentSheet === 'function') {
             window.chartManager.init(window.workbook, window.currentSheet); // Re-init or ensure it's initialized
             window.chartManager.setCurrentSheet(window.currentSheet); // Update sheet reference
        } else {
             console.error("Chart Manager not properly initialized or accessible.");
             updateStatus('Chart functionality not available.', 'error');
             return;
        }

        // Call the showEnhancedChartDialog function from the ChartManager singleton
        if (window.chartManager && typeof window.chartManager.showChartDialog === 'function') {
            window.chartManager.showChartDialog(selectedRange);
        } else {
             console.error("showChartDialog function not found on ChartManager.");
             updateStatus('Could not open chart dialog.', 'error');
        }
    });
}

function setupFormulaBar() {
    const formulaBar = document.getElementById('formulaBar');
    const formulaInput = document.getElementById('formulaInput');

    if (formulaInput) {
        formulaInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                applyFormula(formulaInput.value);
                e.preventDefault();
            }
        });
    }
}

// Core Excel file handling functions
async function handleFileSelect(event) {
    const file = event.target.files[0];
    const toolbar = document.getElementById('toolbar');
    const formulaBar = document.getElementById('formulaBar');
    const spreadsheetContainer = document.getElementById('spreadsheetContainer');
    const sheetTabsContainer = document.getElementById('sheetTabsContainer');

    if (toolbar) toolbar.style.display = 'none';
    if (sheetTabsContainer) sheetTabsContainer.style.display = 'none';
    if (spreadsheetContainer) spreadsheetContainer.innerHTML = '<p>Please upload an Excel file to begin editing.</p>';

    if (!file) {
        updateStatus('No file selected', 'error');
        return;
    }

    updateStatus(`Loading file "${file.name}"...`);

    try {
        // Check if this is a password-protected XLSM file
        if (file.name.toLowerCase().endsWith('.xlsm')) {
            console.log('Detected XLSM file, checking if it is password protected...');

            // Import the protected file opener
            const ProtectedFileOpenerModule = await import('./protected-file-opener.js');
            const protectedFileOpener = new ProtectedFileOpenerModule.default();

            // Try to open as a protected file
            console.log('Attempting to open as a protected file...');
            const success = await protectedFileOpener.openProtectedFile(file);

            if (success) {
                // File was opened successfully as a protected file
                console.log('File opened successfully as a protected file');
                // The protectedFileOpener has already set up the workbook and UI
                updateStatus(`Protected file "${file.name}" opened successfully`, 'success');
                return;
            }

            // If not a protected file or password was incorrect, continue with normal loading
            console.log('Not a valid protected file or password was incorrect, trying as regular Excel file');
            updateStatus('Not a valid protected file or password was incorrect. Trying to open as a regular Excel file...', 'info');
        }

        // Load the workbook using our utility
        workbook = await utils.excel.loadWorkbook(file);
        console.log("Workbook loaded:", workbook);

        if (!workbook) {
            throw new Error("Failed to load workbook.");
        }

        // Store the original filename
        workbook.originalFilename = file.name;

        // Set the workbook in the global scope so it can be accessed by other modules
        window.workbook = workbook;
        console.log("Workbook set in global scope:", window.workbook);

        // Get the first sheet
        currentSheet = utils.excel.getFirstSheet(workbook);
        console.log("Current sheet object:", currentSheet);

        if (!currentSheet) {
            throw new Error("First sheet not found in the workbook.");
        }

        if (typeof currentSheet !== 'object' || currentSheet === null) {
            throw new Error("The first sheet could not be properly accessed or is not a valid object.");
        }

        // Initialize the sheet manager
        sheetManager.initialize(workbook, (sheet, _index) => {
            // This callback is called when the active sheet changes
            currentSheet = sheet;
            window.currentSheet = sheet;
            renderSheet();
        });

        // Show toolbar, formula bar, and sheet tabs
        if (toolbar) toolbar.style.display = 'flex';
        if (formulaBar) formulaBar.style.display = 'flex';
        if (sheetTabsContainer) sheetTabsContainer.style.display = 'flex';

        // Add to recent files if we have a path
        if (file.path) {
            try {
                const storageManager = await import('./storage_manager.js');
                storageManager.default.addRecentFile(file.path, file.name);
            } catch (storageError) {
                console.error('Error adding to recent files:', storageError);
            }
        }

        updateStatus(`File "${file.name}" loaded successfully. Double-click a cell to edit.`, 'success');
    } catch (error) {
        console.error('Error loading file:', error);
        updateStatus('Error loading file: ' + error.message, 'error');
        workbook = null;
        currentSheet = null;
        window.workbook = null;
        window.currentSheet = null;
        if (spreadsheetContainer) spreadsheetContainer.innerHTML = `<p>Error loading file: ${error.message}. Please try another one or check the console for details.</p>`;
    }
}

async function handleFileSave() {
    if (!workbook) {
        updateStatus('No workbook to save', 'error');
        return;
    }

    try {
        updateStatus('Saving file...');
        console.log('Preparing workbook for save operation...');

        // Use the original filename if available, otherwise use a default name
        let filename = workbook.originalFilename || 'spreadsheet.xlsx';

        // IMPORTANT: Ensure merged cells are properly synchronized before saving
        try {
            console.log('Ensuring merged cells are properly saved...');
            await ensureMergedCellsPersistence(workbook);
        } catch (mergeError) {
            console.warn('Non-critical error processing merged cells:', mergeError);
            // Continue with save despite merge error
        }

        // Add to recent files
        try {
            const storageManager = await import('./storage_manager.js');
            // Just store the filename without path for recent files
            storageManager.default.addRecentFile(filename, filename);
        } catch (storageError) {
            console.error('Error adding to recent files:', storageError);
        }

        // Save the workbook using our utility
        console.log('Executing workbook save operation...');
        await utils.excel.saveWorkbook(workbook, filename);

        updateStatus(`File "${filename}" saved successfully`, 'success');
    } catch (error) {
        console.error('Error saving file:', error);
        updateStatus('Error saving file: ' + error.message, 'error');
    }
}

// Save As functionality - opens a dialog to save with a new name
async function handleFileSaveAs() {
    if (!workbook) {
        updateStatus('No workbook to save', 'error');
        return;
    }

    try {
        updateStatus('Preparing Save As...');

        const initialFilename = workbook.originalFilename || 'spreadsheet.xlsx';

        const bodyHtml = `
            <div class="form-group">
                <label for="filenameInputModal">Filename:</label>
                <input type="text" id="filenameInputModal" value="${initialFilename}" class="form-control" style="width: 100%;">
            </div>
        `;

        const footerButtons = [
            {
                text: 'Cancel',
                onClick: () => { /* Modal closes by default */ },
                isPrimary: false
            },
            {
                text: 'Save',
                id: 'saveAsConfirmBtnModal', // Keep ID if other logic relies on it, though direct click is better
                onClick: async () => {
                    const filenameInput = document.getElementById('filenameInputModal');
                    const newFilename = filenameInput.value.trim();
                    if (!newFilename) {
                        // Consider using a small non-modal notification for this error
                        alert('Please enter a filename');
                        // To prevent modal from closing if validation fails:
                        // throw new Error('Filename required'); // or handle differently
                        return; // Or, if modal should stay open, handle click logic carefully
                    }

                    let filenameToSave;
                    if (newFilename.endsWith('.xlsx') || newFilename.endsWith('.envent')) {
                        filenameToSave = newFilename;
                    } else {
                        filenameToSave = `${newFilename}.xlsx`;
                    }

                    workbook.originalFilename = filenameToSave;

                    try {
                        const storageManager = await import('./storage_manager.js');
                        storageManager.default.addRecentFile(filenameToSave, filenameToSave);
                    } catch (storageError) {
                        console.error('Error adding to recent files:', storageError);
                    }

                    await utils.excel.saveWorkbook(workbook, filenameToSave);
                    updateStatus(`File "${filenameToSave}" saved successfully`, 'success');
                    // Modal will close automatically due to closesModal !== false by default
                },
                isPrimary: true,
                // closesModal: false // example: if you want to keep modal open after click sometimes
            }
        ];

        const modalInstance = createModal('Save As', bodyHtml, footerButtons);

        // Focus the filename input after modal is created
        // The input ID is now 'filenameInputModal'
        const filenameInputElem = document.getElementById('filenameInputModal');
        if (filenameInputElem) {
            filenameInputElem.focus();
            filenameInputElem.select();
        }

    } catch (error) {
        console.error('Error in Save As:', error);
        updateStatus('Error in Save As: ' + error.message, 'error');
    }
}

// Show Export Modal with options for different formats
function showExportModal() {
    if (!workbook) {
        updateStatus('No workbook to export', 'error');
        return;
    }

    // Create a standardized export modal
    const modalInstance = createExportModal({
        title: 'Export Workbook',
        size: 'large'
    });

    // Show the modal
    modalInstance.show();

    // Excel export
    document.getElementById('exportExcelBtn').addEventListener('click', async () => {
        try {
            const filename = (workbook.originalFilename || 'spreadsheet') + '.xlsx';
            await utils.excel.saveWorkbook(workbook, filename);
            updateStatus(`Exported to Excel: ${filename}`, 'success');
            modalInstance.close();
        } catch (error) {
            console.error('Error exporting to Excel:', error);
            updateStatus('Error exporting to Excel: ' + error.message, 'error');
        }
    });

    // Envent export (.envent)
    document.getElementById('exportEnventBtn').addEventListener('click', async () => {
        try {
            // Generate a filename with .envent extension
            const baseFilename = workbook.originalFilename || 'spreadsheet';
            const filename = baseFilename.replace(/\.(xlsx|xls|envent)$/, '') + '.envent';

            // Use the same Excel save function but with .envent extension
            // This ensures the file is a standard XLSX internally
            await utils.excel.saveWorkbook(workbook, filename);

            updateStatus(`Exported to Envent: ${filename}`, 'success');
            modalInstance.close();
        } catch (error) {
            console.error('Error exporting to Envent:', error);
            updateStatus('Error exporting to Envent: ' + error.message, 'error');
        }
    });

    // CSV export
    document.getElementById('exportCsvBtn').addEventListener('click', async () => {
        try {
            // Convert workbook data to CSV format
            const data = [];
            const sheet = currentSheet;

            // Get sheet data range
            const range = sheet.usedRange();
            const startRow = range.startCell().rowNumber();
            const startCol = range.startCell().columnNumber();
            const endRow = range.endCell().rowNumber();
            const endCol = range.endCell().columnNumber();

            // Extract data
            for (let r = startRow; r <= endRow; r++) {
                const row = [];
                for (let c = startCol; c <= endCol; c++) {
                    row.push(sheet.cell(r, c).value() || '');
                }
                data.push(row);
            }

            // Export to CSV
            const filename = (workbook.originalFilename || 'spreadsheet').replace('.xlsx', '') + '.csv';
            utils.export.exportToCsv(data, filename);
            updateStatus(`Exported to CSV: ${filename}`, 'success');
            modalInstance.close();
        } catch (error) {
            console.error('Error exporting to CSV:', error);
            updateStatus('Error exporting to CSV: ' + error.message, 'error');
        }
    });

    // PDF export
    document.getElementById('exportPdfBtn').addEventListener('click', async () => {
        try {
            // Create a container for the PDF content
            const container = document.createElement('div');
            container.style.padding = '20px';
            container.style.backgroundColor = 'white';

            // Add workbook title
            const title = document.createElement('h1');
            title.textContent = workbook.originalFilename || 'Spreadsheet';
            container.appendChild(title);

            // Create a table for the sheet data
            const table = document.createElement('table');
            table.style.width = '100%';
            table.style.borderCollapse = 'collapse';
            table.style.marginTop = '20px';

            // Get sheet data range
            const sheet = currentSheet;
            const range = sheet.usedRange();
            const startRow = range.startCell().rowNumber();
            const startCol = range.startCell().columnNumber();
            const endRow = range.endCell().rowNumber();
            const endCol = range.endCell().columnNumber();

            // Create table rows and cells
            for (let r = startRow; r <= endRow; r++) {
                const tr = document.createElement('tr');

                for (let c = startCol; c <= endCol; c++) {
                    const cell = sheet.cell(r, c);
                    const value = cell.value() || '';
                    const td = document.createElement(r === startRow ? 'th' : 'td');
                    td.textContent = value;
                    td.style.border = '1px solid #ddd';
                    td.style.padding = '8px';

                    // Apply basic styles
                    if (cell.style('bold')) {
                        td.style.fontWeight = 'bold';
                    }
                    if (cell.style('italic')) {
                        td.style.fontStyle = 'italic';
                    }
                    if (cell.style('underline')) {
                        td.style.textDecoration = 'underline';
                    }
                    if (cell.style('fontColor')) {
                        td.style.color = cell.style('fontColor');
                    }
                    if (cell.style('fill')) {
                        td.style.backgroundColor = cell.style('fill');
                    }

                    tr.appendChild(td);
                }

                table.appendChild(tr);
            }

            container.appendChild(table);
            document.body.appendChild(container);

            // Get page setup settings if available
            const pageSetup = workbook.pageSetup || {
                pageSize: 'A4',
                orientation: 'portrait',
                margins: {
                    top: 1.0,
                    right: 0.75,
                    bottom: 1.0,
                    left: 0.75
                },
                scaling: 100
            };

            // Convert page setup settings to PDF options
            const pdfOptions = {
                pageSize: pageSetup.pageSize,
                scale: 2, // Higher scale for better quality
                margin: {
                    top: pageSetup.margins.top * 72, // Convert inches to points
                    right: pageSetup.margins.right * 72,
                    bottom: pageSetup.margins.bottom * 72,
                    left: pageSetup.margins.left * 72
                }
            };

            // Export to PDF with enhanced options
            const filename = (workbook.originalFilename || 'spreadsheet').replace('.xlsx', '') + '.pdf';
            await utils.export.exportToPdf(container, filename, pdfOptions);

            // Remove the temporary container after a short delay to ensure PDF generation is complete
            setTimeout(() => {
                if (document.body.contains(container)) {
                    document.body.removeChild(container);
                }
            }, 500);

            updateStatus(`Exported to PDF: ${filename}`, 'success');
            modalInstance.close();
        } catch (error) {
            console.error('Error exporting to PDF:', error);
            updateStatus('Error exporting to PDF: ' + error.message, 'error');
        }
    });

    // HTML export
    document.getElementById('exportHtmlBtn').addEventListener('click', async () => {
        try {
            updateStatus('Preparing HTML export...', 'info');

            // Import the HTML export utility
            const HtmlExportModule = await import('../js/html-export.js');
            const HtmlExport = HtmlExportModule.default;

            // Generate filename
            const filename = (workbook.originalFilename || 'spreadsheet').replace('.xlsx', '') + '.html';

            // Export to HTML
            await HtmlExport.exportToHtml(workbook, currentSheet, filename);

            updateStatus(`Exported to HTML: ${filename}`, 'success');
            modalInstance.close();
        } catch (error) {
            console.error('Error exporting to HTML:', error);
            updateStatus('Error exporting to HTML: ' + error.message, 'error');
        }
    });
}

// Show Page Setup Modal
function showPageSetupModal() {
    if (!workbook) {
        updateStatus('No workbook loaded', 'error');
        return;
    }

    // Create a standardized page setup modal
    const modalInstance = createPageSetupModal({
        onApply: () => {
            // Get form values
            const pageSize = document.getElementById('pageSize').value;
            const orientation = document.getElementById('pageOrientation').value;
            const marginTop = parseFloat(document.getElementById('marginTop').value);
            const marginRight = parseFloat(document.getElementById('marginRight').value);
            const marginBottom = parseFloat(document.getElementById('marginBottom').value);
            const marginLeft = parseFloat(document.getElementById('marginLeft').value);
            const scaling = 100; // Default scaling

            // Store page setup settings in the workbook
            if (workbook) {
                workbook.pageSetup = {
                    pageSize,
                    orientation,
                    margins: {
                        top: marginTop,
                        right: marginRight,
                        bottom: marginBottom,
                        left: marginLeft
                    },
                    scaling
                };

                updateStatus('Page setup settings applied', 'success');
                modalInstance.close();
            } else {
                updateStatus('No workbook loaded', 'error');
            }
        }
    });

    // Show the modal
    modalInstance.show();
}

// Show Workbook Settings Modal
function showWorkbookSettingsModal() {
    if (!workbook) {
        updateStatus('No workbook loaded', 'error');
        return;
    }

    // Create a standardized workbook settings modal
    const modalInstance = createWorkbookSettingsModal({
        onApply: () => {
            // Get form values
            const title = document.getElementById('workbookName').value;
            const author = document.getElementById('workbookAuthor').value;
            const company = document.getElementById('workbookCompany').value;
            const description = document.getElementById('workbookDescription').value;
            const calculationMode = document.querySelector('input[name="calcMode"]:checked').value;

            // Store workbook settings
            workbook.title = title;
            workbook.author = author;
            workbook.company = company;
            workbook.description = description;
            workbook.calculationMode = calculationMode;

            updateStatus('Workbook settings applied', 'success');
            modalInstance.close();
        }
    });

    // Show the modal
    modalInstance.show();

    // Set initial values after modal is created
    setTimeout(() => {
        if (document.getElementById('workbookName')) {
            document.getElementById('workbookName').value = workbook.title || '';
        }
        if (document.getElementById('workbookAuthor')) {
            document.getElementById('workbookAuthor').value = workbook.author || '';
        }
        if (document.getElementById('workbookCompany')) {
            document.getElementById('workbookCompany').value = workbook.company || '';
        }
        if (document.getElementById('workbookDescription')) {
            document.getElementById('workbookDescription').value = workbook.description || '';
        }

        // Set calculation mode
        const calcMode = workbook.calculationMode || 'auto';
        const calcRadio = document.getElementById(calcMode === 'auto' ? 'calcAuto' : 'calcManual');
        if (calcRadio) {
            calcRadio.checked = true;
        }
    }, 100);
}

async function renderSheet() {
    if (!currentSheet) return;

    const container = document.getElementById('spreadsheetContainer');
    container.innerHTML = '';

    const table = document.createElement('table');
    table.className = 'excel-table';
    table.id = 'excelTable';

    // Add a flag to track when rendering is complete

    // Create header row with column letters
    const headerRow = document.createElement('tr');
    const cornerCell = document.createElement('th');
    cornerCell.className = 'corner-cell';
    headerRow.appendChild(cornerCell);

    for (let col = 1; col <= 26; col++) {
        const th = document.createElement('th');
        th.className = 'column-header';
        th.textContent = String.fromCharCode(64 + col);
        th.dataset.col = col;
        headerRow.appendChild(th);
    }
    table.appendChild(headerRow);

    // Create data rows
    for (let row = 1; row <= 100; row++) {
        const tr = document.createElement('tr');

        // Row header (numbers)
        const th = document.createElement('th');
        th.className = 'row-header';
        th.textContent = row;
        th.dataset.row = row;
        tr.appendChild(th);

        // Data cells
        for (let col = 1; col <= 26; col++) {
            const td = document.createElement('td');
            const cell = currentSheet.cell(row, col);

            // Create an input element for editing
            const input = document.createElement('input');
            input.type = 'text';
            input.className = 'cell-input';
            input.value = cell.value() || '';
            input.dataset.row = row;
            input.dataset.col = col;
            input.readOnly = true; // Initially read-only, will be made editable on double-click

            // Apply cell styles to the input
            applyCellStyles(cell, input);

            // Set up the cell with the input
            td.appendChild(input);
            td.dataset.row = row;
            td.dataset.col = col;

            // Add event listeners
            input.addEventListener('focus', () => handleCellFocus(input));
            input.addEventListener('blur', () => handleCellBlur(input));
            input.addEventListener('input', () => handleCellInput(input));

            // Double-click to edit
            td.addEventListener('dblclick', () => {
                input.readOnly = false;
                input.focus();
                input.select();
            });

            // Single click for selection
            td.addEventListener('click', (e) => {
                activeCell = td;
                // Prevent immediate editing on click
                if (e.target === input) {
                    e.preventDefault();
                }
                handleCellFocus(input);
            });

            tr.appendChild(td);
        }
        table.appendChild(tr);
    }

    container.appendChild(table);

    // Setup selection handling
    setupCellSelection(table);

    console.log('Sheet rendered successfully');

    // Dispatch a custom event to notify that the table has been rendered
    // This will be used by other modules like improved_cell_resizer.js
    setTimeout(() => {
        document.dispatchEvent(new CustomEvent('table-rendered', {
            detail: {
                table: table,
                sheetName: currentSheet && typeof currentSheet.name === 'function' ? currentSheet.name() : 'Sheet1'
            }
        }));
        console.log('Dispatched table-rendered event');
    }, 100); // Small delay to ensure the DOM is fully updated
}

// Expose renderSheet globally for ProtectedFileOpener and other modules
window.updateSpreadsheetView = renderSheet;

// New function to handle workbooks loaded by external modules (e.g., ProtectedFileOpener)
function handleWorkbookLoadedExternally() {
    if (!window.workbook) {
        console.error("handleWorkbookLoadedExternally called, but window.workbook is not set.");
        updateStatus("Error processing loaded file. Workbook data not found.", "error");
        return;
    }

    // Update main_script.js's internal module-level variables
    workbook = window.workbook;
    try {
        currentSheet = utils.excel.getFirstSheet(workbook);
    } catch (e) {
        console.error("Error getting first sheet in handleWorkbookLoadedExternally:", e);
        updateStatus("Error: Could not access sheet data in the opened file.", "error");
        currentSheet = null; // Ensure it's null if there's an error
    }

    if (!currentSheet) {
        console.error("First sheet not found or invalid in the externally loaded workbook.");
        updateStatus("Error: No valid sheet found in the opened file. The file might be empty or corrupted.", "error");
        // Optionally hide spreadsheet UI elements if no valid sheet
        const uiElementsToHide = ['toolbar', 'formulaBar', 'sheetTabsContainer'];
        uiElementsToHide.forEach(id => {
            const elem = document.getElementById(id);
            if (elem) elem.style.display = 'none';
        });
        const spreadsheetContainer = document.getElementById('spreadsheetContainer');
        if (spreadsheetContainer) spreadsheetContainer.innerHTML = '<p>Error: No valid sheet found in the file.</p>';
        return;
    }

    // Ensure window.currentSheet is also aligned with the first sheet initially
    window.currentSheet = currentSheet;

    console.log("Initializing sheetManager with externally loaded workbook:", workbook);
    // Initialize the sheet manager with the new workbook
    sheetManager.initialize(workbook, (sheet, _index) => {
        currentSheet = sheet; // Update module-level currentSheet
        window.currentSheet = sheet; // Update global currentSheet
        console.log("sheetManager callback: currentSheet set, calling renderSheet for sheet:", sheet ? (typeof sheet.name === 'function' ? sheet.name() : sheet.name) : 'undefined');
        renderSheet(); // This will now use the correct, updated module-level currentSheet
    });

    // Show UI elements that might have been hidden or need to be consistently shown
    const uiElementsToShow = ['toolbar', 'formulaBar', 'sheetTabsContainer'];
    uiElementsToShow.forEach(id => {
        const elem = document.getElementById(id);
        if (elem) elem.style.display = 'flex';
    });

    // The updateStatus message about successful load is typically handled by the calling module (ProtectedFileOpener)
    console.log(`Workbook "${workbook.originalFilename || 'a protected file'}" processed by handleWorkbookLoadedExternally.`);
}

// Expose the new handler globally
window.handleWorkbookLoadedExternally = handleWorkbookLoadedExternally;

function applyCellStyles(xlsxCell, htmlElement) {
    if (!xlsxCell || !htmlElement) return;

    // Reset styles first
    htmlElement.style.fontWeight = '';
    htmlElement.style.fontStyle = '';
    htmlElement.style.textDecoration = '';
    htmlElement.style.color = '';
    htmlElement.style.backgroundColor = '';
    htmlElement.style.textAlign = '';
    htmlElement.style.fontSize = '';
    htmlElement.style.fontFamily = '';

    // Apply font styles
    if (xlsxCell.style('bold')) {
        htmlElement.style.fontWeight = 'bold';
    }

    if (xlsxCell.style('italic')) {
        htmlElement.style.fontStyle = 'italic';
    }

    if (xlsxCell.style('underline')) {
        htmlElement.style.textDecoration = 'underline';
    }

    // Apply colors
    const fontColor = xlsxCell.style('fontColor');
    if (fontColor) {
        // Handle different formats of color values
        const colorValue = typeof fontColor === 'string' ? fontColor :
                          (fontColor && fontColor.rgb ? fontColor.rgb :
                           fontColor && fontColor.hex ? fontColor.hex : fontColor);
        if (colorValue) {
            htmlElement.style.color = typeof colorValue === 'string' && colorValue.startsWith('#') ?
                                     colorValue : '#' + colorValue;
        }
    }

    const fill = xlsxCell.style('fill');
    if (fill) {
        // Handle different formats of color values
        let colorValue;
        if (typeof fill === 'string') {
            colorValue = fill;
        } else if (fill && fill.color) {
            colorValue = fill.color.rgb || fill.color.hex || fill.color;
        } else {
            colorValue = fill;
        }

        if (colorValue) {
            htmlElement.style.backgroundColor = typeof colorValue === 'string' && colorValue.startsWith('#') ?
                                              colorValue : '#' + colorValue;
        }
    }

    // Apply alignment
    const alignment = xlsxCell.style('horizontalAlignment');
    if (alignment) {
        htmlElement.style.textAlign = alignment;
    }

    // Apply font size
    const fontSize = xlsxCell.style('fontSize');
    if (fontSize) {
        htmlElement.style.fontSize = `${fontSize}px`;
    }

    // Apply font family
    const fontFamily = xlsxCell.style('fontFamily');
    if (fontFamily) {
        htmlElement.style.fontFamily = fontFamily;
    }

    // Apply number format if needed
    const format = xlsxCell.style('numberFormat');
    if (format) {
        // Format the value based on the number format
        // This is a simplified implementation
        const value = xlsxCell.value();
        if (typeof value === 'number') {
            switch (format) {
                case '0':
                    htmlElement.value = Math.round(value).toString();
                    break;
                case '0.00':
                    htmlElement.value = value.toFixed(2);
                    break;
                case '#,##0':
                    htmlElement.value = Math.round(value).toLocaleString();
                    break;
                case '#,##0.00':
                    htmlElement.value = value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                    break;
                case '0%':
                    htmlElement.value = `${Math.round(value * 100)}%`;
                    break;
                case '0.00%':
                    htmlElement.value = `${(value * 100).toFixed(2)}%`;
                    break;
                case '$#,##0.00':
                    htmlElement.value = `$${value.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
                    break;
            }
        }
    }
}

function updateStatus(message, type = 'info') {
    const statusElement = document.getElementById('status');
    if (statusElement) {
        statusElement.textContent = message;
        statusElement.className = type;
    }
}

// Cell handling functions
function handleCellFocus(input) {
    const formulaBar = document.getElementById('formulaBar');
    const formulaInput = document.getElementById('formulaInput');

    if (!formulaBar || !formulaInput || !currentSheet) {
        return;
    }

    formulaBar.style.display = 'flex';

    try {
        // Make sure row and column are valid numbers
        const row = parseInt(input.dataset.row);
        const col = parseInt(input.dataset.col);

        if (isNaN(row) || isNaN(col) || row < 1 || col < 1) {
            console.warn('Invalid cell coordinates:', input.dataset.row, input.dataset.col);
            formulaInput.value = '';
            return;
        }

        // Get cell value safely
        const cell = currentSheet.cell(row, col);
        if (cell) {
            const value = cell.value();
            formulaInput.value = value || '';

            // Update the font size input to reflect the current cell's font size
            updateFontSizeInput();
        } else {
            formulaInput.value = '';
        }
    } catch (error) {
        console.error('Error in handleCellFocus:', error);
        formulaInput.value = '';
    }
}

function handleCellBlur(input) {
    // Make the input read-only again when it loses focus
    input.readOnly = true;

    if (!currentSheet) {
        return;
    }

    try {
        // Make sure row and column are valid numbers
        const row = parseInt(input.dataset.row);
        const col = parseInt(input.dataset.col);

        if (isNaN(row) || isNaN(col) || row < 1 || col < 1) {
            console.warn('Invalid cell coordinates:', input.dataset.row, input.dataset.col);
            return;
        }

        // Get the original value before updating
        const cell = currentSheet.cell(row, col);
        const oldValue = cell ? cell.value() : '';
        const newValue = input.value;

        // Only record history if the value actually changed
        if (oldValue !== newValue) {
            // Record state before making changes
            if (historyManager) {
                historyManager.recordState(currentSheet, {
                    type: 'cellEdit',
                    description: `Edit cell ${row},${col}`,
                    row: row,
                    col: col
                });
            }

            // Update the cell value in the workbook
            if (cell) {
                cell.value(newValue);
                // Log for debugging
                console.log(`Cell (${row}, ${col}) updated with value: ${newValue}`);
            }
        }
    } catch (error) {
        console.error('Error in handleCellBlur:', error);
    }
}

function handleCellInput(input) {
    const formulaInput = document.getElementById('formulaInput');
    if (formulaInput) {
        formulaInput.value = input.value;
    }
}

function applyFormula(formula) {
    const activeElement = document.activeElement;

    try {
        // Check if the active element is our cell input
        if (activeElement.classList.contains('cell-input')) {
            // Make sure row and column are valid numbers
            const row = parseInt(activeElement.dataset.row);
            const col = parseInt(activeElement.dataset.col);

            // Validate row and column values
            if (isNaN(row) || isNaN(col) || row < 1 || col < 1) {
                console.error('Invalid cell coordinates:', activeElement.dataset.row, activeElement.dataset.col);
                return;
            }

            activeElement.value = formula;
            currentSheet.cell(row, col).value(formula);
        } else if (activeCell) {
            // If no active input but we have an active cell, find its input
            const input = activeCell.querySelector('.cell-input');
            if (input) {
                // Make sure row and column are valid numbers
                const row = parseInt(input.dataset.row);
                const col = parseInt(input.dataset.col);

                // Validate row and column values
                if (isNaN(row) || isNaN(col) || row < 1 || col < 1) {
                    console.error('Invalid cell coordinates:', input.dataset.row, input.dataset.col);
                    return;
                }

                input.value = formula;
                currentSheet.cell(row, col).value(formula);
            }
        }
    } catch (error) {
        console.error('Error applying formula:', error);
        updateStatus('Error applying formula: ' + error.message, 'error');
    }
}

// Style application functions
function applyStyle(style, value) {
    // If there's no workbook or sheet, exit
    if (!currentSheet) return;

    // Check if we have a selection range
    if (selectedRange && selectedRange.start && selectedRange.end) {
        // Apply style to all cells in the selection range
        try {
            // Get the range of cells
            const startRow = Math.min(selectedRange.start.r, selectedRange.end.r);
            const endRow = Math.max(selectedRange.start.r, selectedRange.end.r);
            const startCol = Math.min(selectedRange.start.c, selectedRange.end.c);
            const endCol = Math.max(selectedRange.start.c, selectedRange.end.c);

            // Record state before making changes
            if (historyManager) {
                const actionDescription = value ?
                    `Apply ${style}=${value} to range` :
                    `Toggle ${style} in range`;

                historyManager.recordState(currentSheet, {
                    type: 'style',
                    description: actionDescription,
                    range: {
                        start: { r: startRow, c: startCol },
                        end: { r: endRow, c: endCol }
                    },
                    style: style,
                    value: value
                });
            }

            // Apply style to each cell in the range
            for (let row = startRow; row <= endRow; row++) {
                for (let col = startCol; col <= endCol; col++) {
                    const cell = currentSheet.cell(row, col);
                    // Apply the style directly to the cell
                    applyStyleToCell(cell, style, value);

                    // Also update the HTML element for immediate visual feedback
                    const inputElement = document.querySelector(`.cell-input[data-row="${row}"][data-col="${col}"]`);
                    if (inputElement) {
                        applyCellStyles(cell, inputElement);
                    }
                }
            }

            console.log(`Applied style ${style} to cell range from (${startRow},${startCol}) to (${endRow},${endCol})`);
            return;
        } catch (error) {
            console.error('Error applying style to range:', error);
            updateStatus('Error applying style to range: ' + error.message, 'error');
        }
    }

    // If no range is selected, fall back to active cell
    const activeElement = document.activeElement;
    let targetInput;

    // Determine which cell to style
    if (activeElement.classList.contains('cell-input')) {
        // If the active element is a cell input
        targetInput = activeElement;
    } else if (activeCell) {
        // If we have an active cell but it's not the active element
        targetInput = activeCell.querySelector('.cell-input');
    }

    // If we couldn't find a target, exit
    if (!targetInput) return;

    // Get the cell from the workbook
    // Make sure row and column are valid numbers
    const row = parseInt(targetInput.dataset.row);
    const col = parseInt(targetInput.dataset.col);

    // Validate row and column values
    if (isNaN(row) || isNaN(col) || row < 1 || col < 1) {
        console.error('Invalid cell coordinates:', targetInput.dataset.row, targetInput.dataset.col);
        return;
    }

    try {
        // Record state before making changes
        if (historyManager) {
            const actionDescription = value ?
                `Apply ${style}=${value} to cell` :
                `Toggle ${style} in cell`;

            historyManager.recordState(currentSheet, {
                type: 'style',
                description: actionDescription,
                cell: { row, col },
                style: style,
                value: value
            });
        }

        const cell = currentSheet.cell(row, col);

        // Apply the style using the helper function
        applyStyleToCell(cell, style, value, targetInput);

        // Apply the style directly to the input element for immediate feedback
        applyCellStyles(cell, targetInput);

        // Log for debugging
        console.log(`Applied style ${style} to cell (${row}, ${col})`);
    } catch (error) {
        console.error('Error applying style:', error);
        updateStatus('Error applying style: ' + error.message, 'error');
    }
}

// Helper function to apply a style to a cell
function applyStyleToCell(cell, style, value, targetInput = null) {
    if (!cell) return;

    switch (style) {
        case 'bold':
            cell.style('bold', !cell.style('bold'));
            break;
        case 'italic':
            cell.style('italic', !cell.style('italic'));
            break;
        case 'underline':
            cell.style('underline', !cell.style('underline'));
            break;
        case 'fontColor':
            // Remove the # from the color value if present
            cell.style('fontColor', value.startsWith('#') ? value.substring(1) : value);
            break;
        case 'fill':
            // Remove the # from the color value if present
            cell.style('fill', value.startsWith('#') ? value.substring(1) : value);
            break;
        case 'horizontalAlignment':
            cell.style('horizontalAlignment', value);
            break;
        case 'fontFamily':
            cell.style('fontFamily', value);
            // Don't apply directly to targetInput here as it's handled by applyCellStyles
            break;
        case 'numberFormat':
            cell.style('numberFormat', value);

            // Apply the number format immediately to the cell value if we have a target input
            if (targetInput) {
                const cellValue = cell.value();
                if (typeof cellValue === 'number') {
                    // Format the value based on the number format
                    switch (value) {
                        case '0':
                            targetInput.value = Math.round(cellValue).toString();
                            break;
                        case '0.00':
                            targetInput.value = cellValue.toFixed(2);
                            break;
                        case '#,##0':
                            targetInput.value = Math.round(cellValue).toLocaleString();
                            break;
                        case '#,##0.00':
                            targetInput.value = cellValue.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',');
                            break;
                        case '0%':
                            targetInput.value = `${Math.round(cellValue * 100)}%`;
                            break;
                        case '0.00%':
                            targetInput.value = `${(cellValue * 100).toFixed(2)}%`;
                            break;
                        case '$#,##0.00':
                            targetInput.value = `$${cellValue.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ',')}`;
                            break;
                        default:
                            // For General format, just use the raw value
                            targetInput.value = cellValue.toString();
                    }
                }
            }
            break;
    }
}

// Update the font size dropdown to reflect the current cell's font size
function updateFontSizeInput() {
    if (!currentSheet) return;

    const fontSizeSelect = document.getElementById('fontSizeSelect');
    if (!fontSizeSelect) return;

    let targetInput;

    // Determine which cell to get the font size from
    if (activeCell) {
        targetInput = activeCell.querySelector('.cell-input');
    } else {
        return; // No active cell to get font size from
    }

    // Get the cell from the workbook
    const row = parseInt(targetInput.dataset.row);
    const col = parseInt(targetInput.dataset.col);

    // Validate row and column values
    if (isNaN(row) || isNaN(col) || row < 1 || col < 1) {
        return;
    }

    try {
        const cell = currentSheet.cell(row, col);
        const fontSize = cell.style('fontSize') || defaultFontSize;

        // Check if the font size exists in the dropdown options
        let optionExists = false;
        for (let i = 0; i < fontSizeSelect.options.length; i++) {
            if (parseInt(fontSizeSelect.options[i].value) === parseInt(fontSize)) {
                fontSizeSelect.selectedIndex = i;
                optionExists = true;
                break;
            }
        }

        // If the size doesn't exist in the dropdown, add it
        if (!optionExists) {
            // Create a new option
            const newOption = document.createElement('option');
            newOption.value = fontSize;
            newOption.text = fontSize;

            // Find the right position to insert (to keep options sorted)
            let insertIndex = 0;
            while (insertIndex < fontSizeSelect.options.length &&
                   parseInt(fontSizeSelect.options[insertIndex].value) < parseInt(fontSize)) {
                insertIndex++;
            }

            // Insert the option at the right position
            fontSizeSelect.add(newOption, insertIndex);
            fontSizeSelect.selectedIndex = insertIndex;
        }
    } catch (error) {
        console.error('Error updating font size dropdown:', error);
    }
}

// Apply a specific font size directly to the selected cell(s)
function applyFontSizeDirectly(newSize) {
    if (!currentSheet) return;

    // Check if we have a selection range
    if (selectedRange && selectedRange.start && selectedRange.end) {
        try {
            // Get the range of cells
            const startRow = Math.min(selectedRange.start.r, selectedRange.end.r);
            const endRow = Math.max(selectedRange.start.r, selectedRange.end.r);
            const startCol = Math.min(selectedRange.start.c, selectedRange.end.c);
            const endCol = Math.max(selectedRange.start.c, selectedRange.end.c);

            // Record state before making changes
            if (historyManager) {
                historyManager.recordState(currentSheet, {
                    type: 'style',
                    description: `Apply font size ${newSize}px to range`,
                    range: {
                        start: { r: startRow, c: startCol },
                        end: { r: endRow, c: endCol }
                    },
                    style: 'fontSize',
                    value: newSize
                });
            }

            // Apply font size to each cell in the range
            for (let row = startRow; row <= endRow; row++) {
                for (let col = startCol; col <= endCol; col++) {
                    const cell = currentSheet.cell(row, col);
                    cell.style('fontSize', newSize);

                    // Update the HTML element for immediate visual feedback
                    const inputElement = document.querySelector(`.cell-input[data-row="${row}"][data-col="${col}"]`);
                    if (inputElement) {
                        inputElement.style.fontSize = `${newSize}px`;
                    }
                }
            }

            console.log(`Applied font size ${newSize} to cell range from (${startRow},${startCol}) to (${endRow},${endCol})`);
            return;
        } catch (error) {
            console.error('Error applying font size to range:', error);
            updateStatus('Error applying font size to range: ' + error.message, 'error');
        }
    }

    // If no range is selected, fall back to active cell
    let targetInput;

    // Determine which cell to adjust
    if (activeCell) {
        targetInput = activeCell.querySelector('.cell-input');
    } else {
        return; // No active cell to apply font size to
    }

    // Get the cell from the workbook
    const row = parseInt(targetInput.dataset.row);
    const col = parseInt(targetInput.dataset.col);

    // Validate row and column values
    if (isNaN(row) || isNaN(col) || row < 1 || col < 1) {
        console.error('Invalid cell coordinates:', targetInput.dataset.row, targetInput.dataset.col);
        return;
    }

    try {
        // Record state before making changes
        if (historyManager) {
            historyManager.recordState(currentSheet, {
                type: 'style',
                description: `Apply font size ${newSize}px to cell`,
                cell: { row, col },
                style: 'fontSize',
                value: newSize
            });
        }

        const cell = currentSheet.cell(row, col);

        // Apply the new font size
        cell.style('fontSize', newSize);

        // Apply the style directly to the input element for immediate feedback
        targetInput.style.fontSize = `${newSize}px`;

        console.log(`Applied font size ${newSize} to cell (${row}, ${col})`);
    } catch (error) {
        console.error('Error applying font size:', error);
        updateStatus('Error applying font size: ' + error.message, 'error');
    }
}

function adjustFontSize(delta) {
    // Get the active element
    const activeElement = document.activeElement;
    let targetInput;

    // Determine which cell to adjust
    if (activeElement.classList.contains('cell-input')) {
        // If the active element is a cell input
        targetInput = activeElement;
    } else if (activeCell) {
        // If we have an active cell but it's not the active element
        targetInput = activeCell.querySelector('.cell-input');
    }

    // If we couldn't find a target or there's no workbook, exit
    if (!targetInput || !currentSheet) return;

    // Get the cell from the workbook
    // Make sure row and column are valid numbers
    const row = parseInt(targetInput.dataset.row);
    const col = parseInt(targetInput.dataset.col);

    // Validate row and column values
    if (isNaN(row) || isNaN(col) || row < 1 || col < 1) {
        console.error('Invalid cell coordinates:', targetInput.dataset.row, targetInput.dataset.col);
        return;
    }

    try {
        const cell = currentSheet.cell(row, col);

        // Get the current font size
        let currentSize = cell.style('fontSize') || defaultFontSize;
        let newSize = parseInt(currentSize) + delta;
        if (newSize < 6) newSize = 6;
        if (newSize > 72) newSize = 72;

        // Record state before making changes
        if (historyManager) {
            historyManager.recordState(currentSheet, {
                type: 'style',
                description: `Adjust font size to ${newSize}px`,
                cell: { row, col },
                style: 'fontSize',
                value: newSize
            });
        }

        // Apply the new font size
        cell.style('fontSize', newSize);

        // Apply the style directly to the input element for immediate feedback
        targetInput.style.fontSize = `${newSize}px`;

        // Update the font size dropdown
        updateFontSizeInput();

        // Log for debugging
        console.log(`Adjusted font size to ${newSize}px for cell (${row}, ${col})`);
    } catch (error) {
        console.error('Error adjusting font size:', error);
        updateStatus('Error adjusting font size: ' + error.message, 'error');
    }
}

// Row/Column operations
function insertRow(position) {
    if (!currentSheet) {
        updateStatus('No sheet loaded', 'error');
        return;
    }

    let row;

    // Get the row from the active cell or selected range
    if (activeCell) {
        row = parseInt(activeCell.dataset.row);
    } else if (selectedRange) {
        row = position === 'above' ?
            Math.min(selectedRange.start.r, selectedRange.end.r) :
            Math.max(selectedRange.start.r, selectedRange.end.r);
    } else {
        updateStatus('Please select a cell or range first', 'error');
        return;
    }

    try {
        // Insert row based on position
        if (position === 'above') {
            // Insert row above
            for (let c = 1; c <= 26; c++) {
                // Shift all cells down
                for (let r = 100; r > row; r--) {
                    const value = currentSheet.cell(r - 1, c).value();
                    const styles = {};

                    // Copy styles
                    const styleProps = ['bold', 'italic', 'underline', 'fontColor', 'fill', 'horizontalAlignment', 'fontSize', 'numberFormat'];
                    styleProps.forEach(prop => {
                        const style = currentSheet.cell(r - 1, c).style(prop);
                        if (style !== undefined) {
                            styles[prop] = style;
                        }
                    });

                    // Set value and styles to the new cell
                    currentSheet.cell(r, c).value(value);
                    Object.entries(styles).forEach(([prop, val]) => {
                        currentSheet.cell(r, c).style(prop, val);
                    });
                }

                // Clear the inserted row
                currentSheet.cell(row, c).value(null);

                // Clear styles
                const styleProps = ['bold', 'italic', 'underline', 'fontColor', 'fill', 'horizontalAlignment', 'fontSize', 'numberFormat'];
                styleProps.forEach(prop => {
                    currentSheet.cell(row, c).style(prop, null);
                });
            }
        } else {
            // Insert row below
            for (let c = 1; c <= 26; c++) {
                // Shift all cells down
                for (let r = 100; r > row + 1; r--) {
                    const value = currentSheet.cell(r - 1, c).value();
                    const styles = {};

                    // Copy styles
                    const styleProps = ['bold', 'italic', 'underline', 'fontColor', 'fill', 'horizontalAlignment', 'fontSize', 'numberFormat'];
                    styleProps.forEach(prop => {
                        const style = currentSheet.cell(r - 1, c).style(prop);
                        if (style !== undefined) {
                            styles[prop] = style;
                        }
                    });

                    // Set value and styles to the new cell
                    currentSheet.cell(r, c).value(value);
                    Object.entries(styles).forEach(([prop, val]) => {
                        currentSheet.cell(r, c).style(prop, val);
                    });
                }

                // Clear the inserted row
                currentSheet.cell(row + 1, c).value(null);

                // Clear styles
                const styleProps = ['bold', 'italic', 'underline', 'fontColor', 'fill', 'horizontalAlignment', 'fontSize', 'numberFormat'];
                styleProps.forEach(prop => {
                    currentSheet.cell(row + 1, c).style(prop, null);
                });
            }
        }

        // Re-render the sheet
        renderSheet();
        updateStatus(`Row inserted ${position}`, 'success');
    } catch (error) {
        console.error('Error inserting row:', error);
        updateStatus('Error inserting row: ' + error.message, 'error');
    }
}

function deleteRows() {
    if (!currentSheet) {
        updateStatus('No sheet loaded', 'error');
        return;
    }

    let startRow, endRow;

    // Get the row range from the active cell or selected range
    if (activeCell) {
        startRow = endRow = parseInt(activeCell.dataset.row);
    } else if (selectedRange) {
        startRow = Math.min(selectedRange.start.r, selectedRange.end.r);
        endRow = Math.max(selectedRange.start.r, selectedRange.end.r);
    } else {
        updateStatus('Please select a cell or range first', 'error');
        return;
    }

    try {
        // Count rows to delete
        const rowCount = endRow - startRow + 1;

        // Delete rows by shifting cells up
        for (let c = 1; c <= 26; c++) {
            for (let r = startRow; r <= 100 - rowCount; r++) {
                const value = currentSheet.cell(r + rowCount, c).value();
                const styles = {};

                // Copy styles
                const styleProps = ['bold', 'italic', 'underline', 'fontColor', 'fill', 'horizontalAlignment', 'fontSize', 'numberFormat'];
                styleProps.forEach(prop => {
                    const style = currentSheet.cell(r + rowCount, c).style(prop);
                    if (style !== undefined) {
                        styles[prop] = style;
                    }
                });

                // Set value and styles to the new cell
                currentSheet.cell(r, c).value(value);
                Object.entries(styles).forEach(([prop, val]) => {
                    currentSheet.cell(r, c).style(prop, val);
                });
            }

            // Clear the last rows
            for (let r = 100 - rowCount + 1; r <= 100; r++) {
                currentSheet.cell(r, c).value(null);

                // Clear styles
                const styleProps = ['bold', 'italic', 'underline', 'fontColor', 'fill', 'horizontalAlignment', 'fontSize', 'numberFormat'];
                styleProps.forEach(prop => {
                    currentSheet.cell(r, c).style(prop, null);
                });
            }
        }

        // Reset selection
        activeCell = null;
        selectedRange = null;

        // Re-render the sheet
        renderSheet();
        updateStatus(`${rowCount} row(s) deleted`, 'success');
    } catch (error) {
        console.error('Error deleting rows:', error);
        updateStatus('Error deleting rows: ' + error.message, 'error');
    }
}

function insertColumn(position) {
    if (!currentSheet) {
        updateStatus('No sheet loaded', 'error');
        return;
    }

    let col;

    // Get the column from the active cell or selected range
    if (activeCell) {
        col = parseInt(activeCell.dataset.col);
    } else if (selectedRange) {
        col = position === 'left' ?
            Math.min(selectedRange.start.c, selectedRange.end.c) :
            Math.max(selectedRange.start.c, selectedRange.end.c);
    } else {
        updateStatus('Please select a cell or range first', 'error');
        return;
    }

    try {
        // Insert column based on position
        if (position === 'left') {
            // Insert column to the left
            for (let r = 1; r <= 100; r++) {
                // Shift all cells right
                for (let c = 26; c > col; c--) {
                    const value = currentSheet.cell(r, c - 1).value();
                    const styles = {};

                    // Copy styles
                    const styleProps = ['bold', 'italic', 'underline', 'fontColor', 'fill', 'horizontalAlignment', 'fontSize', 'numberFormat'];
                    styleProps.forEach(prop => {
                        const style = currentSheet.cell(r, c - 1).style(prop);
                        if (style !== undefined) {
                            styles[prop] = style;
                        }
                    });

                    // Set value and styles to the new cell
                    currentSheet.cell(r, c).value(value);
                    Object.entries(styles).forEach(([prop, val]) => {
                        currentSheet.cell(r, c).style(prop, val);
                    });
                }

                // Clear the inserted column
                currentSheet.cell(r, col).value(null);

                // Clear styles
                const styleProps = ['bold', 'italic', 'underline', 'fontColor', 'fill', 'horizontalAlignment', 'fontSize', 'numberFormat'];
                styleProps.forEach(prop => {
                    currentSheet.cell(r, col).style(prop, null);
                });
            }
        } else {
            // Insert column to the right
            for (let r = 1; r <= 100; r++) {
                // Shift all cells right
                for (let c = 26; c > col + 1; c--) {
                    const value = currentSheet.cell(r, c - 1).value();
                    const styles = {};

                    // Copy styles
                    const styleProps = ['bold', 'italic', 'underline', 'fontColor', 'fill', 'horizontalAlignment', 'fontSize', 'numberFormat'];
                    styleProps.forEach(prop => {
                        const style = currentSheet.cell(r, c - 1).style(prop);
                        if (style !== undefined) {
                            styles[prop] = style;
                        }
                    });

                    // Set value and styles to the new cell
                    currentSheet.cell(r, c).value(value);
                    Object.entries(styles).forEach(([prop, val]) => {
                        currentSheet.cell(r, c).style(prop, val);
                    });
                }

                // Clear the inserted column
                currentSheet.cell(r, col + 1).value(null);

                // Clear styles
                const styleProps = ['bold', 'italic', 'underline', 'fontColor', 'fill', 'horizontalAlignment', 'fontSize', 'numberFormat'];
                styleProps.forEach(prop => {
                    currentSheet.cell(r, col + 1).style(prop, null);
                });
            }
        }

        // Re-render the sheet
        renderSheet();
        updateStatus(`Column inserted to the ${position}`, 'success');
    } catch (error) {
        console.error('Error inserting column:', error);
        updateStatus('Error inserting column: ' + error.message, 'error');
    }
}

function deleteColumns() {
    if (!currentSheet) {
        updateStatus('No sheet loaded', 'error');
        return;
    }

    let startCol, endCol;

    // Get the column range from the active cell or selected range
    if (activeCell) {
        startCol = endCol = parseInt(activeCell.dataset.col);
    } else if (selectedRange) {
        startCol = Math.min(selectedRange.start.c, selectedRange.end.c);
        endCol = Math.max(selectedRange.start.c, selectedRange.end.c);
    } else {
        updateStatus('Please select a cell or range first', 'error');
        return;
    }

    try {
        // Count columns to delete
        const colCount = endCol - startCol + 1;

        // Delete columns by shifting cells left
        for (let r = 1; r <= 100; r++) {
            for (let c = startCol; c <= 26 - colCount; c++) {
                const value = currentSheet.cell(r, c + colCount).value();
                const styles = {};

                // Copy styles
                const styleProps = ['bold', 'italic', 'underline', 'fontColor', 'fill', 'horizontalAlignment', 'fontSize', 'numberFormat'];
                styleProps.forEach(prop => {
                    const style = currentSheet.cell(r, c + colCount).style(prop);
                    if (style !== undefined) {
                        styles[prop] = style;
                    }
                });

                // Set value and styles to the new cell
                currentSheet.cell(r, c).value(value);
                Object.entries(styles).forEach(([prop, val]) => {
                    currentSheet.cell(r, c).style(prop, val);
                });
            }

            // Clear the last columns
            for (let c = 26 - colCount + 1; c <= 26; c++) {
                currentSheet.cell(r, c).value(null);

                // Clear styles
                const styleProps = ['bold', 'italic', 'underline', 'fontColor', 'fill', 'horizontalAlignment', 'fontSize', 'numberFormat'];
                styleProps.forEach(prop => {
                    currentSheet.cell(r, c).style(prop, null);
                });
            }
        }

        // Reset selection
        activeCell = null;
        selectedRange = null;

        // Re-render the sheet
        renderSheet();
        updateStatus(`${colCount} column(s) deleted`, 'success');
    } catch (error) {
        console.error('Error deleting columns:', error);
        updateStatus('Error deleting columns: ' + error.message, 'error');
    }
}

// Data operations
function sortData() {
    const activeCell = document.activeElement;
    if (!activeCell || activeCell.tagName !== 'TD' || !currentSheet) return;

    const col = parseInt(activeCell.dataset.col);
    const range = currentSheet.usedRange();
    const startRow = range.startCell().rowNumber();
    const endRow = range.endCell().rowNumber();

    currentSheet.range(startRow, col, endRow, col).sort('asc');
    renderSheet();
}

function toggleFilter() {
    console.log('Toggling filter...');

    if (!workbook || !currentSheet) {
        updateStatus('No workbook is open', 'error');
        return;
    }

    if (!selectedRange) {
        updateStatus('Please select a range of cells to filter', 'error');
        return;
    }

    try {
        // Get the normalized range
        const startRow = Math.min(selectedRange.start.r, selectedRange.end.r);
        const endRow = Math.max(selectedRange.start.r, selectedRange.end.r);
        const startCol = Math.min(selectedRange.start.c, selectedRange.end.c);
        const endCol = Math.max(selectedRange.start.c, selectedRange.end.c);

        console.log(`Applying filter to range from (${startRow},${startCol}) to (${endRow},${endCol})`);

        // Create range reference in Excel format (e.g., A1:B2)
        const startColName = numberToColumnLetter(startCol);
        const endColName = numberToColumnLetter(endCol);
        const rangeRef = `${startColName}${startRow}:${endColName}${endRow}`;
        console.log(`Range reference for filter: ${rangeRef}`);

        // Check if filter is already active
        let isFilterActive = currentSheet.hasAutoFilter();

        if (isFilterActive) {
            // Remove the filter if it exists
            currentSheet.autoFilter(null);
            updateStatus('Filter removed', 'success');
        } else {
            // Apply auto filter to the range
            currentSheet.autoFilter(rangeRef);

            // Flag the workbook as modified
            try {
                if (window.historyManager && typeof window.historyManager.addState === 'function') {
                    window.historyManager.addState(workbook, 'Applied filter');
                } else {
                    // Fall back to simple modification flag if history manager isn't available
                    if (!window.workbookModified) window.workbookModified = true;
                }
            } catch (historyError) {
                console.warn('Could not add to history:', historyError);
                // Continue execution despite history error
            }

            updateStatus('Filter applied', 'success');
        }

        // Update UI
        renderSheet();

    } catch (error) {
        console.error('Error toggling filter:', error);
        updateStatus('Failed to toggle filter: ' + error.message, 'error');
    }
}

function toggleFormulaBar() {
    const formulaBar = document.getElementById('formulaBar');
    if (formulaBar) {
        formulaBar.style.display = formulaBar.style.display === 'none' ? 'flex' : 'none';
    }
}

/**
 * Handle undo button click
 */
function handleUndo() {
    if (!currentSheet) {
        updateStatus('No sheet to undo changes on', 'error');
        return;
    }

    try {
        const success = historyManager.undo(currentSheet);
        if (success) {
            updateStatus('Undo successful', 'success');
        } else {
            updateStatus('Nothing to undo', 'info');
        }
    } catch (error) {
        console.error('Error performing undo:', error);
        updateStatus('Error performing undo: ' + error.message, 'error');
    }
}

/**
 * Handle redo button click
 */
function handleRedo() {
    if (!currentSheet) {
        updateStatus('No sheet to redo changes on', 'error');
        return;
    }

    try {
        const success = historyManager.redo(currentSheet);
        if (success) {
            updateStatus('Redo successful', 'success');
        } else {
            updateStatus('Nothing to redo', 'info');
        }
    } catch (error) {
        console.error('Error performing redo:', error);
        updateStatus('Error performing redo: ' + error.message, 'error');
    }
}

/**
 * Handle format painter button click
 */
function handleFormatPainter() {
    if (!currentSheet) {
        updateStatus('No sheet to use format painter on', 'error');
        return;
    }

    try {
        const success = toggleFormatPainter(currentSheet, selectedRange, activeCell);
        if (success) {
            updateStatus('Format painter activated. Click on a cell or select a range to apply formatting.', 'info');
        } else {
            updateStatus('Format painter deactivated', 'info');
        }
    } catch (error) {
        console.error('Error with format painter:', error);
        updateStatus('Error with format painter: ' + error.message, 'error');
    }
}

// Chart operations
function showChartDialog(chartWrapper) {
    if (!workbook || !currentSheet) {
        updateStatus('Please open a workbook first', 'error');
        return;
    }

    // First, close any existing modal dialogs to prevent duplicates
    const existingModals = document.querySelectorAll('.modal, .excel-modal, .chart-dialog');
    existingModals.forEach(modal => {
        if (modal && modal.parentNode) {
            modal.parentNode.removeChild(modal);
        }
    });

    // Initialize the chart manager if not already initialized
    if (!window.chartManagerInitialized) {
        if (window.ChartManager && typeof window.ChartManager.init === 'function') {
            window.ChartManager.init(workbook, currentSheet);
            window.chartManagerInitialized = true;
        } else {
            console.error('ChartManager.init is not available');
            updateStatus('Chart manager is not properly loaded', 'error');
            return;
        }
    }

    // Check if we have a selected range
    if (!selectedRange) {
        updateStatus('Please select a range of cells first', 'error');
        return;
    }

    // Store the selected range in Chrome storage for improved chart creation
    try {
        // Try to use Chrome storage integration if available
        if (typeof chrome !== 'undefined' && chrome.storage && chrome.storage.local) {
            chrome.storage.local.set({ 'chart_selected_range': selectedRange }, function() {
                console.log('Selected range saved to Chrome storage');
            });
        } else {
            // Fallback to localStorage
            localStorage.setItem('chart_selected_range', JSON.stringify(selectedRange));
            console.log('Selected range saved to localStorage');
        }
    } catch (error) {
        console.warn('Could not save selected range to storage:', error);
    }

    // Show the chart dialog with the selected range
    if (typeof window.ChartManager.showChartDialog === 'function') {
        window.ChartManager.showChartDialog(selectedRange);
    } else {
        console.error('ChartManager.showChartDialog is not available');
        updateStatus('Chart dialog functionality is not available', 'error');
    }
}

// Get the selected range as a string (e.g., "A1:B10")
function getSelectedRangeString() {
    if (!selectedRange) return null;

    // Make sure start is always before end
    const startRow = Math.min(selectedRange.start.r, selectedRange.end.r);
    const endRow = Math.max(selectedRange.start.r, selectedRange.end.r);
    const startCol = Math.min(selectedRange.start.c, selectedRange.end.c);
    const endCol = Math.max(selectedRange.start.c, selectedRange.end.c);

    // Convert column numbers to letters
    const startColLetter = numberToColumnLetter(startCol);
    const endColLetter = numberToColumnLetter(endCol);

    return `${startColLetter}${startRow}:${endColLetter}${endRow}`;
}

// Convert column number to letter (1 = A, 2 = B, etc.)
function numberToColumnLetter(column) {
    let temp, letter = '';
    while (column > 0) {
        temp = (column - 1) % 26;
        letter = String.fromCharCode(temp + 65) + letter;
        column = (column - temp - 1) / 26;
    }
    return letter;
}

// Enable range selection mode
function enableRangeSelectionMode(callback) {
    const table = document.getElementById('excelTable');
    if (!table) {
        updateStatus('Spreadsheet not found', 'error');
        return;
    }

    let selectionStart = null;
    let selectionEnd = null;
    let isSelecting = false;
    let selectionOverlay = document.createElement('div');
    selectionOverlay.className = 'range-selection-overlay';
    selectionOverlay.style.position = 'absolute';
    selectionOverlay.style.zIndex = '10';
    // Append to the table's parent container instead of body for better positioning
    const tableContainer = table.parentElement || document.body;
    tableContainer.appendChild(selectionOverlay);

    // Add event listeners for selection
    table.addEventListener('mousedown', handleMouseDown);
    table.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
    document.addEventListener('keydown', handleKeyDown);

    // Highlight cells when mouse is pressed
    function handleMouseDown(e) {
        if (e.target.tagName === 'TD') {
            isSelecting = true;
            selectionStart = {
                r: parseInt(e.target.dataset.row),
                c: parseInt(e.target.dataset.col)
            };
            selectionEnd = { ...selectionStart };
            updateSelectionOverlay();
            e.preventDefault();
        }
    }

    // Update selection when mouse moves
    function handleMouseMove(e) {
        if (isSelecting && e.target.tagName === 'TD') {
            selectionEnd = {
                r: parseInt(e.target.dataset.row),
                c: parseInt(e.target.dataset.col)
            };
            updateSelectionOverlay();
        }
    }

    // Finalize selection when mouse is released
    function handleMouseUp() {
        isSelecting = false;
    }

    // Handle key presses (Enter to confirm selection)
    function handleKeyDown(e) {
        if (e.key === 'Enter' && selectionStart && selectionEnd) {
            // Clean up
            cleanup();

            // Convert selection to range string
            const startRow = Math.min(selectionStart.r, selectionEnd.r);
            const endRow = Math.max(selectionStart.r, selectionEnd.r);
            const startCol = Math.min(selectionStart.c, selectionEnd.c);
            const endCol = Math.max(selectionStart.c, selectionEnd.c);

            const startColLetter = numberToColumnLetter(startCol);
            const endColLetter = numberToColumnLetter(endCol);

            const rangeString = `${startColLetter}${startRow}:${endColLetter}${endRow}`;

            // Call the callback with the range string
            callback(rangeString);
        } else if (e.key === 'Escape') {
            // Cancel selection
            cleanup();
            callback(null);
        }
    }

    // Update the selection overlay
    function updateSelectionOverlay() {
        if (!selectionStart || !selectionEnd) return;

        // Get the table position
        const tableRect = table.getBoundingClientRect();
        const tableContainer = table.parentElement;

        // Get the start and end cells
        const startCell = table.querySelector(`td[data-row="${selectionStart.r}"][data-col="${selectionStart.c}"]`);
        const endCell = table.querySelector(`td[data-row="${selectionEnd.r}"][data-col="${selectionEnd.c}"]`);

        if (!startCell || !endCell) return;

        // Get the cell positions
        const startRect = startCell.getBoundingClientRect();
        const endRect = endCell.getBoundingClientRect();

        // Calculate the overlay position and size
        // We need to account for the table's position in the viewport and its scroll position
        const left = Math.min(startRect.left, endRect.left) - tableRect.left + tableContainer.scrollLeft;
        const top = Math.min(startRect.top, endRect.top) - tableRect.top + tableContainer.scrollTop;
        const width = Math.abs(endRect.left - startRect.left) + endRect.width;
        const height = Math.abs(endRect.top - startRect.top) + endRect.height;

        // Update the overlay
        selectionOverlay.style.position = 'absolute';
        selectionOverlay.style.left = `${left}px`;
        selectionOverlay.style.top = `${top}px`;
        selectionOverlay.style.width = `${width}px`;
        selectionOverlay.style.height = `${height}px`;
        selectionOverlay.style.display = 'block';
    }

    // Clean up event listeners
    function cleanup() {
        table.removeEventListener('mousedown', handleMouseDown);
        table.removeEventListener('mousemove', handleMouseMove);
        document.removeEventListener('mouseup', handleMouseUp);
        document.removeEventListener('keydown', handleKeyDown);

        // Remove the selection overlay from its parent
        if (selectionOverlay && selectionOverlay.parentNode) {
            selectionOverlay.parentNode.removeChild(selectionOverlay);
        }
    }
}

function createChart(type, range, title, color = '#008FFB') {
    try {
        // Parse the range
        if (!range) {
            updateStatus('Please specify a data range', 'error');
            return;
        }

        const [start, end] = range.split(':');
        if (!start || !end) {
            updateStatus('Invalid range format. Use format like A1:B10', 'error');
            return;
        }

        // Extract data from the range
        const data = extractDataFromRangeString(start, end);
        if (!data || data.categories.length === 0 || data.values.length === 0) {
            updateStatus('Could not extract data from the specified range', 'error');
            return;
        }

        // Create chart container with a draggable header
        const chartWrapper = document.createElement('div');
        chartWrapper.className = 'chart-wrapper';

        // Add a header with controls
        const chartHeader = document.createElement('div');
        chartHeader.className = 'chart-header';
        chartHeader.innerHTML = `
            <div class="chart-title">${title}</div>
            <div class="chart-controls">
                <button class="chart-control edit-btn" title="Edit Chart"><span class="material-icons">edit</span></button>
                <button class="chart-control delete-btn" title="Delete Chart"><span class="material-icons">delete</span></button>
            </div>
        `;
        chartWrapper.appendChild(chartHeader);

        // Create the chart container
        const chartContainer = document.createElement('div');
        chartContainer.className = 'chart-container';
        chartContainer.style.width = '500px';
        chartContainer.style.height = '300px';
        chartWrapper.appendChild(chartContainer);

        // Add chart to the spreadsheet container
        const spreadsheetContainer = document.getElementById('spreadsheetContainer');
        spreadsheetContainer.appendChild(chartWrapper);

        // Create the chart
        const chart = utils.chart.createChart(
            chartContainer,
            type,
            data,
            {
                title: title,
                colors: [color]
            }
        );

        // Store chart data for editing
        chartWrapper.dataset.chartType = type;
        chartWrapper.dataset.dataRange = range;
        chartWrapper.dataset.chartTitle = title;
        chartWrapper.dataset.chartColor = color;

        // Make the chart draggable
        makeChartDraggable(chartWrapper, chartHeader);

        // Add event listeners for chart controls
        const editBtn = chartHeader.querySelector('.edit-btn');
        const deleteBtn = chartHeader.querySelector('.delete-btn');

        editBtn.addEventListener('click', () => {
            // Show the chart dialog with current settings
            showChartDialog(chartWrapper);
        });

        deleteBtn.addEventListener('click', () => {
            // Remove the chart
            spreadsheetContainer.removeChild(chartWrapper);
            updateStatus('Chart removed', 'success');
        });

        // Close the modal
        const modal = document.querySelector('.modal');
        if (modal) {
            document.body.removeChild(modal);
        }

        updateStatus('Chart created successfully', 'success');
        return chartWrapper;
    } catch (error) {
        console.error('Error creating chart:', error);
        updateStatus('Error creating chart: ' + error.message, 'error');
        return null;
    }
}

// Make a chart draggable
function makeChartDraggable(chartWrapper, handle) {
    let isDragging = false;
    let offsetX, offsetY;

    handle.style.cursor = 'move';

    handle.addEventListener('mousedown', (e) => {
        isDragging = true;
        offsetX = e.clientX - chartWrapper.getBoundingClientRect().left;
        offsetY = e.clientY - chartWrapper.getBoundingClientRect().top;
        chartWrapper.style.position = 'absolute';
        chartWrapper.style.zIndex = '1000';
        e.preventDefault();
    });

    document.addEventListener('mousemove', (e) => {
        if (!isDragging) return;

        const x = e.clientX - offsetX;
        const y = e.clientY - offsetY;

        chartWrapper.style.left = `${x}px`;
        chartWrapper.style.top = `${y}px`;
    });

    document.addEventListener('mouseup', () => {
        isDragging = false;
    });
}

// This function is now imported from chart-utils.js
// Keeping a wrapper for backward compatibility
function extractDataFromRangeString(start, end) {
    try {
        // Convert Excel column letters to numbers (A=1, B=2, etc.)
        const startCol = start.match(/[A-Z]+/)[0];
        const startRow = parseInt(start.match(/\d+/)[0]);
        const endCol = end.match(/[A-Z]+/)[0];
        const endRow = parseInt(end.match(/\d+/)[0]);

        const startColNum = columnLetterToNumber(startCol);
        const endColNum = columnLetterToNumber(endCol);

        // Create a range object compatible with the imported function
        const range = {
            start: { r: startRow, c: startColNum },
            end: { r: endRow, c: endColNum }
        };

        // Use the imported function
        return extractDataFromRange(range);
    } catch (error) {
        console.error('Error extracting data from range string:', error);
        // Propagate the error instead of returning sample data
        throw new Error(`Failed to extract data from range ${start}:${end}. Original error: ${error.message}`);
    }
}

function columnLetterToNumber(column) {
    let result = 0;
    for (let i = 0; i < column.length; i++) {
        result = result * 26 + (column.charCodeAt(i) - 64);
    }
    return result;
}

// Function to setup cell selection - Excel-like
function setupCellSelection(table) {
    if (!table) return;

    let isSelecting = false;
    let isDragging = false;
    let dragStartCell = null;
    let selectionStart = null;
    let selectionHandle = null;

    // Remove any existing selection overlays
    const existingOverlays = document.querySelectorAll('.selection-overlay, .selection-handle');
    existingOverlays.forEach(overlay => overlay.remove());

    // Create selection overlay
    const selectionOverlay = document.createElement('div');
    selectionOverlay.className = 'selection-overlay';
    selectionOverlay.style.position = 'absolute';
    selectionOverlay.style.zIndex = '10';
    // Append to the table's parent container instead of body for better positioning
    const tableContainer = table.parentElement || document.body;
    tableContainer.appendChild(selectionOverlay);

    // Create selection handle for drag-to-fill
    selectionHandle = document.createElement('div');
    selectionHandle.className = 'selection-handle';
    selectionHandle.style.position = 'absolute';
    selectionHandle.style.zIndex = '11';
    selectionHandle.style.display = 'none';
    // Append to the same container as the selection overlay
    tableContainer.appendChild(selectionHandle);

    // Add event listeners for selection
    table.addEventListener('mousedown', (e) => {
        // Only handle left mouse button
        if (e.button !== 0) return;

        if (e.target.tagName === 'TD' || e.target.closest('td')) {
            // Get the cell
            const cell = e.target.tagName === 'TD' ? e.target : e.target.closest('td');

            // Check if format painter is active
            if (isFormatPainterActive()) {
                // Apply format painter to the clicked cell
                const row = parseInt(cell.dataset.row);
                const col = parseInt(cell.dataset.col);

                // Initialize selection range for format painter
                selectedRange = {
                    start: { r: row, c: col },
                    end: { r: row, c: col }
                };

                // Apply format painter
                applyFormatPainter(currentSheet, selectedRange, cell);

                // Update the cell display
                renderSheet();

                return;
            }

            // Clear previous selection
            clearCellSelectionClasses();

            // Check if clicking on the selection handle
            if (e.target.classList.contains('selection-handle')) {
                isDragging = true;
                dragStartCell = activeCell;
                return;
            }

            isSelecting = true;

            // Set the active cell
            activeCell = cell;
            activeCell.classList.add('active-cell');

            // Get row and column
            const row = parseInt(cell.dataset.row);
            const col = parseInt(cell.dataset.col);

            // Set selection start
            selectionStart = { r: row, c: col };

            // Initialize selection range
            selectedRange = {
                start: { r: row, c: col },
                end: { r: row, c: col }
            };

            // Update selection
            updateCellSelection();

            // Prevent text selection
            e.preventDefault();

            // Focus the cell for keyboard navigation
            if (cell.querySelector('input')) {
                cell.querySelector('input').focus();
            }

            // Update formula bar
            updateFormulaBarWithCell(cell);
        }
    });

    table.addEventListener('mousemove', (e) => {
        if (!isSelecting && !isDragging) return;

        if (e.target.tagName === 'TD' || e.target.closest('td')) {
            // Get the cell
            const cell = e.target.tagName === 'TD' ? e.target : e.target.closest('td');

            // Get row and column
            const row = parseInt(cell.dataset.row);
            const col = parseInt(cell.dataset.col);

            if (isSelecting) {
                // Update selection end
                selectedRange.end = { r: row, c: col };

                // Update selection
                updateCellSelection();
            } else if (isDragging) {
                // Handle drag-to-fill
                // This would be implemented for auto-filling cells
                cell.classList.add('cell-drag-target');
            }
        }
    });

    // Add mouseup handler for format painter
    document.addEventListener('mouseup', (e) => {
        // Check if format painter is active and we're selecting a range
        if (isFormatPainterActive() && isSelecting && selectedRange) {
            // Apply format painter to the selected range
            applyFormatPainter(currentSheet, selectedRange, null);

            // Update the cell display
            renderSheet();
        }
    });

    document.addEventListener('mouseup', () => {
        if (isSelecting) {
            isSelecting = false;

            // Update formula bar with selected range
            updateFormulaBarWithRange();

            // Show selection handle
            updateSelectionHandle();
        }

        if (isDragging) {
            isDragging = false;

            // Handle the end of drag-to-fill
            const dragTargets = document.querySelectorAll('.cell-drag-target');
            if (dragTargets.length > 0) {
                // Implement fill logic here
                dragTargets.forEach(cell => {
                    cell.classList.remove('cell-drag-target');
                });
            }
        }
    });

    // Double-click to edit cell
    table.addEventListener('dblclick', (e) => {
        if (e.target.tagName === 'TD' || e.target.closest('td')) {
            const cell = e.target.tagName === 'TD' ? e.target : e.target.closest('td');
            const input = cell.querySelector('input');

            if (input) {
                input.readOnly = false;
                input.focus();

                // Position cursor at the end
                const length = input.value.length;
                input.setSelectionRange(length, length);
            }
        }
    });

    // Keyboard navigation
    table.addEventListener('keydown', (e) => {
        if (!activeCell) return;

        const row = parseInt(activeCell.dataset.row);
        const col = parseInt(activeCell.dataset.col);

        let newRow = row;
        let newCol = col;

        // Arrow keys for navigation
        switch (e.key) {
            case 'ArrowUp':
                newRow = Math.max(1, row - 1);
                e.preventDefault();
                break;
            case 'ArrowDown':
                newRow = Math.min(100, row + 1);
                e.preventDefault();
                break;
            case 'ArrowLeft':
                newCol = Math.max(1, col - 1);
                e.preventDefault();
                break;
            case 'ArrowRight':
                newCol = Math.min(26, col + 1);
                e.preventDefault();
                break;
            case 'Tab':
                newCol = e.shiftKey ? Math.max(1, col - 1) : Math.min(26, col + 1);
                e.preventDefault();
                break;
            case 'Enter':
                // If editing, commit the change and move down
                const input = activeCell.querySelector('input');
                if (input && !input.readOnly) {
                    input.readOnly = true;
                    newRow = Math.min(100, row + 1);
                } else {
                    newRow = Math.min(100, row + 1);
                }
                e.preventDefault();
                break;
            case 'Escape':
                // Cancel editing
                const escInput = activeCell.querySelector('input');
                if (escInput && !escInput.readOnly) {
                    escInput.readOnly = true;
                    escInput.value = escInput.defaultValue;
                }
                break;
        }

        // Move to the new cell if changed
        if (newRow !== row || newCol !== col) {
            const newCell = table.querySelector(`td[data-row="${newRow}"][data-col="${newCol}"]`);
            if (newCell) {
                // Clear previous selection
                clearCellSelectionClasses();

                // Set new active cell
                activeCell = newCell;
                activeCell.classList.add('active-cell');

                // Update selection
                selectedRange = {
                    start: { r: newRow, c: newCol },
                    end: { r: newRow, c: newCol }
                };

                updateCellSelection();
                updateSelectionHandle();

                // Update formula bar
                updateFormulaBarWithCell(newCell);

                // Focus the cell
                if (newCell.querySelector('input')) {
                    newCell.querySelector('input').focus();
                }
            }
        }
    });

    // Clear all selection classes
    function clearCellSelectionClasses() {
        const selectedCells = table.querySelectorAll('.selected, .active-cell');
        selectedCells.forEach(cell => {
            cell.classList.remove('selected', 'active-cell');
        });
    }

    // Update cell selection based on selectedRange
    function updateCellSelection() {
        if (!selectedRange) return;

        // Clear previous selection except active cell
        const selectedCells = table.querySelectorAll('.selected');
        selectedCells.forEach(cell => {
            cell.classList.remove('selected');
        });

        // Determine the range boundaries
        const startRow = Math.min(selectedRange.start.r, selectedRange.end.r);
        const endRow = Math.max(selectedRange.start.r, selectedRange.end.r);
        const startCol = Math.min(selectedRange.start.c, selectedRange.end.c);
        const endCol = Math.max(selectedRange.start.c, selectedRange.end.c);

        // Update the selectedRange with normalized values
        selectedRange = {
            start: { r: startRow, c: startCol },
            end: { r: endRow, c: endCol }
        };

        // Make selectedRange available globally for style application
        window.selectedRange = selectedRange;

        // Add selected class to cells in range
        for (let r = startRow; r <= endRow; r++) {
            for (let c = startCol; c <= endCol; c++) {
                const cell = table.querySelector(`td[data-row="${r}"][data-col="${c}"]`);
                if (cell && !cell.classList.contains('active-cell')) {
                    cell.classList.add('selected');
                }
            }
        }

        // Update the selection overlay
        updateSelectionOverlay();

        // Dispatch a custom event to notify that a cell has been selected
        // This will be used to update the font size input and other toolbar elements
        document.dispatchEvent(new CustomEvent('cell-selected', {
            detail: {
                range: selectedRange,
                activeCell: activeCell
            }
        }));

        // Update toolbar elements
        const toolbarElements = {
            boldBtn: document.getElementById('boldBtn'),
            italicBtn: document.getElementById('italicBtn'),
            underlineBtn: document.getElementById('underlineBtn'),
            fontColorPicker: document.getElementById('fontColorPicker'),
            bgColorPicker: document.getElementById('bgColorPicker'),
            numberFormatSelect: document.getElementById('numberFormatSelect'),
            // Note: horizontalAlignSelect doesn't exist in the HTML, using individual buttons instead
            alignLeftBtn: document.getElementById('alignLeftBtn'),
            alignCenterBtn: document.getElementById('alignCenterBtn'),
            alignRightBtn: document.getElementById('alignRightBtn'),
            fontFamilySelect: document.getElementById('fontFamilySelect'),
            formatPainterBtn: document.getElementById('formatPainterBtn')
        };

        // Import the updateToolbarForSelection function
        import('./styler.js').then(styler => {
            styler.updateToolbarForSelection(toolbarElements, currentSheet, selectedRange, activeCell);
        }).catch(error => {
            console.error('Error updating toolbar:', error);
        });
    }

    // Function to update the selection overlay - Excel-like with single border
    function updateSelectionOverlay() {
        if (!selectedRange || !table.contains(activeCell)) return;

        // Get the table position
        const tableRect = table.getBoundingClientRect();
        const tableContainer = table.parentElement;

        // Get the start and end cells
        const startRow = Math.min(selectedRange.start.r, selectedRange.end.r);
        const endRow = Math.max(selectedRange.start.r, selectedRange.end.r);
        const startCol = Math.min(selectedRange.start.c, selectedRange.end.c);
        const endCol = Math.max(selectedRange.start.c, selectedRange.end.c);

        const startCell = table.querySelector(`td[data-row="${startRow}"][data-col="${startCol}"]`);
        const endCell = table.querySelector(`td[data-row="${endRow}"][data-col="${endCol}"]`);

        if (!startCell || !endCell) return;

        // Get the cell positions relative to the viewport
        const startRect = startCell.getBoundingClientRect();
        const endRect = endCell.getBoundingClientRect();

        // Calculate the overlay position and size
        // We need to account for the table's position in the viewport and its scroll position
        const left = (startRect.left - tableRect.left) + tableContainer.scrollLeft;
        const top = (startRect.top - tableRect.top) + tableContainer.scrollTop;
        const width = (endRect.right - startRect.left);
        const height = (endRect.bottom - startRect.top);

        // Position the overlay within the table
        selectionOverlay.style.position = 'absolute';
        selectionOverlay.style.left = `${left}px`;
        selectionOverlay.style.top = `${top}px`;
        selectionOverlay.style.width = `${width}px`;
        selectionOverlay.style.height = `${height}px`;

        // Only show the overlay for multi-cell selections
        // For single cell, we rely on the active-cell class styling
        if (startRow === endRow && startCol === endCol) {
            selectionOverlay.style.display = 'none';
        } else {
            selectionOverlay.style.display = 'block';
        }
    }

    // Update the selection handle position
    function updateSelectionHandle() {
        if (!selectedRange || !activeCell) return;

        // Only show handle for single cell selection
        if (selectedRange.start.r !== selectedRange.end.r ||
            selectedRange.start.c !== selectedRange.end.c) {
            selectionHandle.style.display = 'none';
            return;
        }

        const tableRect = table.getBoundingClientRect();
        const cellRect = activeCell.getBoundingClientRect();
        const tableContainer = table.parentElement;

        // Position the handle at the bottom-right corner of the active cell
        // Account for scrolling and table position
        const left = (cellRect.right - tableRect.left) + tableContainer.scrollLeft - 4;
        const top = (cellRect.bottom - tableRect.top) + tableContainer.scrollTop - 4;

        selectionHandle.style.position = 'absolute';
        selectionHandle.style.left = `${left}px`;
        selectionHandle.style.top = `${top}px`;
        selectionHandle.style.display = 'block';
    }

    // Function to update the formula bar with cell value
    function updateFormulaBarWithCell(cell) {
        const formulaInput = document.getElementById('formulaInput');
        if (!formulaInput) return;

        const row = parseInt(cell.dataset.row);
        const col = parseInt(cell.dataset.col);

        try {
            const cellObj = currentSheet.cell(row, col);
            formulaInput.value = cellObj.value() || '';
        } catch (error) {
            console.error('Error getting cell value:', error);
            formulaInput.value = '';
        }
    }

    // Function to update the formula bar with selected range
    function updateFormulaBarWithRange() {
        const formulaInput = document.getElementById('formulaInput');
        if (!formulaInput || !selectedRange) return;

        // If it's a single cell, show the cell value
        if (selectedRange.start.r === selectedRange.end.r && selectedRange.start.c === selectedRange.end.c) {
            updateFormulaBarWithCell(activeCell);
        } else {
            // If it's a range, show the range
            const startCol = numberToColumnLetter(Math.min(selectedRange.start.c, selectedRange.end.c));
            const startRow = Math.min(selectedRange.start.r, selectedRange.end.r);
            const endCol = numberToColumnLetter(Math.max(selectedRange.start.c, selectedRange.end.c));
            const endRow = Math.max(selectedRange.start.r, selectedRange.end.r);

            formulaInput.value = `=${startCol}${startRow}:${endCol}${endRow}`;
        }
    }

    // Update selection on window resize
    window.addEventListener('resize', () => {
        if (selectedRange) {
            // Use a small delay to ensure all layout calculations are complete
            setTimeout(() => {
                updateCellSelection();
                updateSelectionHandle();
            }, 10);
        }
    });

    // Update selection on scroll
    table.addEventListener('scroll', () => {
        if (selectedRange) {
            updateSelectionOverlay();
            updateSelectionHandle();
        }
    });

    // Also listen for scroll events on the table's parent container
    if (table.parentElement) {
        table.parentElement.addEventListener('scroll', () => {
            if (selectedRange) {
                updateSelectionOverlay();
                updateSelectionHandle();
            }
        });
    }

    // Initialize merged cells if any
    initMergedCells();

    // Function to initialize merged cells
    function initMergedCells() {
        if (!currentSheet) return;

        // This would be implemented based on the Excel library being used
        // For now, just a placeholder for the merged cells functionality
        console.log('Initializing merged cells');
    }

    // Initialize column and row resizing functionality
    initResizableColumnsAndRows();

    // Function to make columns and rows resizable
    function initResizableColumnsAndRows() {
        console.log('Initializing resizable columns and rows');

        // Add column resizing capability
        const headerCells = table.querySelectorAll('th');
        headerCells.forEach(th => {
            // Create and append resize handle to header cells
            const resizer = document.createElement('div');
            resizer.className = 'column-resizer';
            th.appendChild(resizer);

            let startX, startWidth, columnIndex;

            // Mouse down event on resizer element
            resizer.addEventListener('mousedown', function(e) {
                startX = e.pageX;
                columnIndex = parseInt(th.dataset.col || 0);
                startWidth = th.offsetWidth;

                // Add event listeners for resize operation
                document.addEventListener('mousemove', resize);
                document.addEventListener('mouseup', stopResize);

                // Add resize class to indicate active resize operation
                th.classList.add('resizing');
                table.classList.add('no-select'); // Prevent text selection during resize

                e.preventDefault();
            });

            // Resize operation
            function resize(e) {
                const width = startWidth + (e.pageX - startX);
                if (width > 30) { // Minimum width
                    th.style.width = width + 'px';

                    // Also resize all corresponding data cells in this column
                    const cells = table.querySelectorAll(`td[data-col="${columnIndex}"]`);
                    cells.forEach(cell => {
                        cell.style.width = width + 'px';
                    });

                    // If using XlsxPopulate, update column width in the sheet
                    if (currentSheet && typeof currentSheet.column === 'function') {
                        try {
                            // Convert pixels to excel column width units (approx.)
                            // Excel column width is in number of characters of the standard font
                            const excelWidth = width / 7; // Approximate conversion
                            currentSheet.column(columnIndex).width(excelWidth);
                        } catch (error) {
                            console.warn('Could not set column width in sheet:', error);
                        }
                    }
                }
            }

            // End resize operation
            function stopResize() {
                document.removeEventListener('mousemove', resize);
                document.removeEventListener('mouseup', stopResize);
                th.classList.remove('resizing');
                table.classList.remove('no-select');

                // Update history if using history manager
                try {
                    if (window.historyManager && typeof window.historyManager.addState === 'function' && workbook) {
                        window.historyManager.addState(workbook, 'Resized column');
                    } else {
                        // Fall back to simple modification flag if history manager isn't available
                        if (!window.workbookModified) window.workbookModified = true;
                    }
                } catch (historyError) {
                    console.warn('Could not add column resize to history:', historyError);
                    // Continue execution despite history error
                }
            }
        });

        // Add row resizing capability
        const rowHeaders = table.querySelectorAll('tr td:first-child');
        rowHeaders.forEach(td => {
            // Create and append resize handle to row headers
            const resizer = document.createElement('div');
            resizer.className = 'row-resizer';
            td.appendChild(resizer);

            let startY, startHeight, rowIndex;

            // Mouse down event on resizer element
            resizer.addEventListener('mousedown', function(e) {
                startY = e.pageY;
                const tr = td.closest('tr');
                rowIndex = parseInt(tr.dataset.row || 0);
                startHeight = tr.offsetHeight;

                // Add event listeners for resize operation
                document.addEventListener('mousemove', resize);
                document.addEventListener('mouseup', stopResize);

                // Add resize class to indicate active resize operation
                tr.classList.add('resizing');
                table.classList.add('no-select'); // Prevent text selection during resize

                e.preventDefault();
            });

            // Resize operation
            function resize(e) {
                const height = startHeight + (e.pageY - startY);
                if (height > 20) { // Minimum height
                    const tr = table.querySelector(`tr[data-row="${rowIndex}"]`);
                    if (tr) {
                        tr.style.height = height + 'px';

                        // If using XlsxPopulate, update row height in the sheet
                        if (currentSheet && typeof currentSheet.row === 'function') {
                            try {
                                // Convert pixels to excel row height units (approx.)
                                // Excel row height is in points
                                const excelHeight = height * 0.75; // Approximate conversion (pixels to points)
                                currentSheet.row(rowIndex).height(excelHeight);
                            } catch (error) {
                                console.warn('Could not set row height in sheet:', error);
                            }
                        }
                    }
                }
            }

            // End resize operation
            function stopResize() {
                document.removeEventListener('mousemove', resize);
                document.removeEventListener('mouseup', stopResize);
                const tr = table.querySelector(`tr[data-row="${rowIndex}"]`);
                if (tr) tr.classList.remove('resizing');
                table.classList.remove('no-select');

                // Update history if using history manager
                try {
                    if (window.historyManager && typeof window.historyManager.addState === 'function' && workbook) {
                        window.historyManager.addState(workbook, 'Resized row');
                    } else {
                        // Fall back to simple modification flag if history manager isn't available
                        if (!window.workbookModified) window.workbookModified = true;
                    }
                } catch (historyError) {
                    console.warn('Could not add row resize to history:', historyError);
                    // Continue execution despite history error
                }
            }
        });
    }
}

// Debug function to log object properties safely
function safeLogObject(name, obj) {
    try {
        console.log(`${name}:`, JSON.stringify(obj));
    } catch (e) {
        console.log(`${name}: [Could not stringify]`, obj);
    }
}

// Merge cells handler function
function handleMergeCells() {
    console.log('===== MERGING CELLS =====');
    console.log('Accessing workbook and current sheet...');

    // Verify XlsxPopulate is loaded
    if (typeof XlsxPopulate === 'undefined') {
        console.error('XlsxPopulate is not defined in the global scope');
        updateStatus('Spreadsheet library not available', 'error');
        return;
    }

    console.log('XlsxPopulate library detected');
    safeLogObject('XlsxPopulate Methods', Object.getOwnPropertyNames(XlsxPopulate));

    // Verify workbook
    if (!workbook) {
        console.error('No active workbook');
        updateStatus('Please open a workbook first', 'error');
        return;
    }

    // Verify methods on workbook
    console.log('Workbook object:', workbook);
    if (typeof workbook.sheet !== 'function') {
        console.error('Workbook.sheet is not a function');
        updateStatus('Invalid workbook object', 'error');
        return;
    }

    // Verify current sheet
    if (!currentSheet) {
        console.error('No active sheet');
        updateStatus('No active sheet', 'error');
        return;
    }

    // Verify methods on sheet
    console.log('Current sheet:', currentSheet);
    if (typeof currentSheet.cell !== 'function' || typeof currentSheet.range !== 'function') {
        console.error('Sheet missing required methods');
        safeLogObject('Sheet methods', Object.getOwnPropertyNames(currentSheet));
        updateStatus('Invalid sheet object', 'error');
        return;
    }

    // Verify we have a selection
    if (!selectedRange) {
        console.error('No cell range selected');
        updateStatus('Please select a range of cells first', 'error');
        return;
    }

    console.log('Selection detected:', selectedRange);

    // Check if it's a single cell (can't merge a single cell)
    if (selectedRange.start.r === selectedRange.end.r && selectedRange.start.c === selectedRange.end.c) {
        console.error('Single cell selected');
        updateStatus('Cannot merge a single cell. Please select multiple cells.', 'error');
        return;
    }

    try {
        // Get the normalized range (ensure start ≤ end for both row and column)
        const startRow = Math.min(selectedRange.start.r, selectedRange.end.r);
        const endRow = Math.max(selectedRange.start.r, selectedRange.end.r);
        const startCol = Math.min(selectedRange.start.c, selectedRange.end.c);
        const endCol = Math.max(selectedRange.start.c, selectedRange.end.c);

        console.log(`Normalized merge range: (${startRow},${startCol}) to (${endRow},${endCol})`);

        // Both XlsxPopulate and SheetJS use different range specifications
        // Try multiple approaches to ensure compatibility with the library in use

        // Direct XlsxPopulate approach
        if (typeof currentSheet.range === 'function') {
            console.log('Using XlsxPopulate range method...');

            try {
                // First approach: Use the range method with row/column indices
                console.log('Attempting merge with numeric coordinates...');
                const range = currentSheet.range(startRow, startCol, endRow, endCol);
                console.log('Range object created:', range);

                if (typeof range.merged === 'function') {
                    console.log('Calling range.merged(true)...');
                    range.merged(true);
                    console.log('Merge operation completed successfully via numeric coordinates');
                } else {
                    throw new Error('Range object does not have a merged function');
                }
            } catch (rangeError) {
                console.warn('First approach failed:', rangeError);

                // Second approach: Try with Excel-style cell references (A1:B2 format)
                console.log('Attempting merge with Excel-style range reference...');
                const startColLetter = numberToColumnLetter(startCol);
                const endColLetter = numberToColumnLetter(endCol);
                const rangeRef = `${startColLetter}${startRow}:${endColLetter}${endRow}`;
                console.log(`Range reference: ${rangeRef}`);

                try {
                    const range = currentSheet.range(rangeRef);
                    console.log('Range object created with reference:', range);

                    if (typeof range.merged === 'function') {
                        range.merged(true);
                        console.log('Merge operation completed successfully via range reference');
                    } else {
                        throw new Error('Range object does not have a merged function');
                    }
                } catch (refError) {
                    console.warn('Second approach failed:', refError);
                    throw new Error('Both merge approaches failed');
                }
            }
        }
        // SheetJS approach - different API
        else if (workbook.Sheets && currentSheet.merges) {
            console.log('Using SheetJS merge approach...');

            if (!currentSheet.merges) {
                currentSheet.merges = [];
            }

            // SheetJS uses a different format for merged cells
            const mergeRange = { s: { r: startRow, c: startCol }, e: { r: endRow, c: endCol } };
            currentSheet.merges.push(mergeRange);
            console.log('Added merge range to sheet.merges array');
        }
        // If no suitable API is found
        else {
            throw new Error('No suitable merge API found in the spreadsheet library');
        }

        // Flag the workbook as modified
        try {
            console.log('Saving merge state to history...');
            if (window.historyManager && typeof window.historyManager.addState === 'function') {
                window.historyManager.addState(workbook, 'Merged cells');
                console.log('Added to history manager');
            } else {
                console.log('No history manager available, setting modified flag');
                window.workbookModified = true;
            }
        } catch (historyError) {
            console.warn('Could not add to history:', historyError);
        }

        // Update the UI to show the merged cells
        console.log('Rendering updated sheet...');
        renderSheet();
        updateStatus('Cells merged successfully', 'success');

        // Apply visual merge in the DOM as fallback
        console.log('Applying visual merge to the DOM as fallback...');
        applyVisualMerge(startRow, startCol, endRow, endCol);

        console.log('Merge operation complete');
    } catch (error) {
        console.error('Error during merge operation:', error);
        updateStatus('Failed to merge cells: ' + error.message, 'error');
    }
}

// Helper function to apply visual merge directly to the DOM elements
// This serves as a fallback if the spreadsheet API merge doesn't render
function applyVisualMerge(startRow, startCol, endRow, endCol) {
    try {
        const table = document.getElementById('excelTable');
        if (!table) {
            console.warn('Excel table not found in DOM');
            return;
        }

        // Find the main cell (top-left corner of merge range)
        const mainCell = table.querySelector(`td[data-row="${startRow}"][data-col="${startCol}"]`);
        if (!mainCell) {
            console.warn('Main cell not found in DOM');
            return;
        }

        // Calculate the size of the merged cell
        const rowSpan = endRow - startRow + 1;
        const colSpan = endCol - startCol + 1;

        // Mark as visually merged
        mainCell.setAttribute('data-merged', 'true');
        mainCell.setAttribute('rowspan', rowSpan);
        mainCell.setAttribute('colspan', colSpan);
        mainCell.classList.add('merged-cell');

        // Hide other cells in the merge range
        for (let r = startRow; r <= endRow; r++) {
            for (let c = startCol; c <= endCol; c++) {
                // Skip the main cell
                if (r === startRow && c === startCol) continue;

                const cell = table.querySelector(`td[data-row="${r}"][data-col="${c}"]`);
                if (cell) {
                    cell.setAttribute('data-merged-into', `${startRow},${startCol}`);
                    cell.style.display = 'none';
                }
            }
        }

        console.log(`Visual merge applied: ${rowSpan}×${colSpan} cells`);
    } catch (error) {
        console.warn('Error applying visual merge:', error);
    }
}

// Unmerge cells handler function
function handleUnmergeCells() {
    console.log('===== UNMERGING CELLS =====');
    console.log('Accessing workbook and current sheet...');

    // Verify XlsxPopulate is loaded
    if (typeof XlsxPopulate === 'undefined') {
        console.error('XlsxPopulate is not defined in the global scope');
        updateStatus('Spreadsheet library not available', 'error');
        return;
    }

    console.log('XlsxPopulate library detected');

    // Verify workbook and sheet
    if (!workbook) {
        console.error('No active workbook');
        updateStatus('Please open a workbook first', 'error');
        return;
    }

    if (!currentSheet) {
        console.error('No active sheet');
        updateStatus('No active sheet', 'error');
        return;
    }

    if (!selectedRange) {
        console.error('No cell range selected');
        updateStatus('Please select a merged cell or range first', 'error');
        return;
    }

    console.log('Selection detected:', selectedRange);

    try {
        // Get the normalized range
        const startRow = Math.min(selectedRange.start.r, selectedRange.end.r);
        const endRow = Math.max(selectedRange.start.r, selectedRange.end.r);
        const startCol = Math.min(selectedRange.start.c, selectedRange.end.c);
        const endCol = Math.max(selectedRange.start.c, selectedRange.end.c);

        console.log(`Normalized unmerge range: (${startRow},${startCol}) to (${endRow},${endCol})`);

        let unmergeSuccessful = false;

        // Direct XlsxPopulate approach
        if (typeof currentSheet.range === 'function') {
            console.log('Using XlsxPopulate unmerge approach...');

            try {
                // Check if the starting cell is merged
                console.log('Checking if starting cell is merged...');
                try {
                    const cell = currentSheet.cell(startRow, startCol);
                    console.log('Starting cell object:', cell);

                    // First, verify the cell object and its methods
                    if (cell && typeof cell === 'object') {
                        console.log('Cell methods:', Object.getOwnPropertyNames(cell));

                        // Check if merged method exists
                        if (typeof cell.merged === 'function') {
                            // Try to call merged() to check status
                            try {
                                const isMerged = cell.merged();
                                console.log('Cell merge status:', isMerged);

                                if (isMerged) {
                                    console.log('Starting cell is merged, unmerging...');
                                    cell.merged(false);
                                    unmergeSuccessful = true;
                                } else {
                                    console.log('Starting cell is not merged, checking entire range...');
                                }
                            } catch (mergedError) {
                                console.warn('Error checking if cell is merged:', mergedError);
                                console.log('Continuing with alternative methods...');
                            }
                        } else {
                            console.log('cell.merged is not a function, trying alternative approach');
                        }
                    } else {
                        console.log('Cell object is not valid');
                    }
                } catch (cellError) {
                    console.warn('Error accessing starting cell:', cellError);
                }

                // If starting cell wasn't merged, check each cell in the range
                if (!unmergeSuccessful) {
                    console.log('Scanning entire range for merged cells...');
                    for (let r = startRow; r <= endRow && !unmergeSuccessful; r++) {
                        for (let c = startCol; c <= endCol && !unmergeSuccessful; c++) {
                            try {
                                const rangeCell = currentSheet.cell(r, c);

                                // Safely check if cell is merged
                                if (rangeCell && typeof rangeCell === 'object' && typeof rangeCell.merged === 'function') {
                                    try {
                                        const isMerged = rangeCell.merged();
                                        if (isMerged) {
                                            console.log(`Found merged cell at (${r},${c}), unmerging...`);
                                            rangeCell.merged(false);
                                            unmergeSuccessful = true;
                                        }
                                    } catch (mergedError) {
                                        console.warn(`Error checking if cell (${r},${c}) is merged:`, mergedError);
                                    }
                                }
                            } catch (cellError) {
                                console.warn(`Error accessing cell (${r},${c}):`, cellError);
                            }
                        }
                    }

                    if (!unmergeSuccessful) {
                        console.log('No merged cells found in range during cell-by-cell scan');
                    }
                }

                // Try unmerging with range if individual cell approach didn't work
                if (!unmergeSuccessful) {
                    console.log('Trying range-based unmerge...');
                    try {
                        const range = currentSheet.range(startRow, startCol, endRow, endCol);
                        console.log('Range object:', range);

                        if (range && typeof range === 'object') {
                            if (typeof range.merged === 'function') {
                                try {
                                    range.merged(false);
                                    unmergeSuccessful = true;
                                    console.log('Range-based unmerge completed successfully');
                                } catch (mergeError) {
                                    console.warn('Error calling range.merged(false):', mergeError);
                                }
                            } else {
                                console.log('range.merged is not a function');
                            }
                        } else {
                            console.log('Invalid range object returned');
                        }
                    } catch (rangeError) {
                        console.warn('Error creating range for unmerge:', rangeError);
                    }
                }
            } catch (error) {
                console.warn('XlsxPopulate unmerge approach failed:', error);
            }
        }
        // SheetJS approach - different API
        else if (workbook.Sheets && currentSheet.merges) {
            console.log('Using SheetJS unmerge approach...');

            if (Array.isArray(currentSheet.merges)) {
                // Find any merges that overlap with our selection
                const originalMerges = [...currentSheet.merges];
                currentSheet.merges = currentSheet.merges.filter(merge => {
                    const mergeStartRow = merge.s.r;
                    const mergeStartCol = merge.s.c;
                    const mergeEndRow = merge.e.r;
                    const mergeEndCol = merge.e.c;

                    // Check if this merge overlaps with our selection
                    const overlaps = (
                        mergeStartRow <= endRow && mergeEndRow >= startRow &&
                        mergeStartCol <= endCol && mergeEndCol >= startCol
                    );

                    if (overlaps) {
                        console.log(`Removing merge: (${mergeStartRow},${mergeStartCol}) to (${mergeEndRow},${mergeEndCol})`);
                        unmergeSuccessful = true;
                    }

                    // Keep this merge if it doesn't overlap
                    return !overlaps;
                });

                if (originalMerges.length !== currentSheet.merges.length) {
                    console.log(`Removed ${originalMerges.length - currentSheet.merges.length} merges`);
                    unmergeSuccessful = true;
                }
            }
        }

        // Apply visual unmerge in the DOM as fallback
        console.log('Applying visual unmerge to the DOM...');
        const visuallyUnmerged = removeVisualMerge(startRow, startCol, endRow, endCol);
        if (visuallyUnmerged) {
            unmergeSuccessful = true;
        }

        // Flag the workbook as modified if something was unmerged
        if (unmergeSuccessful) {
            try {
                console.log('Saving unmerge state to history...');
                if (window.historyManager && typeof window.historyManager.addState === 'function') {
                    window.historyManager.addState(workbook, 'Unmerged cells');
                    console.log('Added to history manager');
                } else {
                    console.log('No history manager available, setting modified flag');
                    window.workbookModified = true;
                }
            } catch (historyError) {
                console.warn('Could not add to history:', historyError);
            }

            // Update the UI
            console.log('Rendering updated sheet after unmerge...');
            renderSheet();
            updateStatus('Cells unmerged successfully', 'success');
        } else {
            console.log('No merged cells found in the selection');
            updateStatus('No merged cells found in the selection', 'warning');
        }

    } catch (error) {
        console.error('Error during unmerge operation:', error);
        updateStatus('Failed to unmerge cells: ' + error.message, 'error');
    }
}

// Function to ensure merged cells persist in the workbook when saved
async function ensureMergedCellsPersistence(workbook) {
    console.log('Syncing merged cells for persistence...');
    if (!workbook) {
        console.warn('No workbook provided to process merged cells');
        return;
    }

    try {
        // Get the active sheet
        const sheet = workbook.activeSheet();
        if (!sheet) {
            console.warn('No active sheet found in workbook');
            return;
        }

        console.log('Active sheet found, processing merged cells...');

        // 1. Find visually merged cells in the DOM
        const table = document.getElementById('excelTable');
        if (!table) {
            console.warn('Excel table not found in DOM');
            return;
        }

        // Find all visually merged cells
        const mergedCells = table.querySelectorAll('td[data-merged="true"], td[rowspan], td[colspan]');
        console.log(`Found ${mergedCells.length} visually merged cells in the DOM`);

        // For each visually merged cell, ensure it's properly merged in the workbook
        Array.from(mergedCells).forEach(cell => {
            try {
                // Get row and column of this merged cell
                const row = parseInt(cell.dataset.row || '0');
                const col = parseInt(cell.dataset.col || '0');

                // Get rowspan and colspan
                const rowSpan = parseInt(cell.getAttribute('rowspan') || '1');
                const colSpan = parseInt(cell.getAttribute('colspan') || '1');

                if (rowSpan > 1 || colSpan > 1) {
                    console.log(`Ensuring persistence of merge at (${row},${col}) with span ${rowSpan}x${colSpan}`);

                    // Calculate end row and column
                    const endRow = row + rowSpan - 1;
                    const endCol = col + colSpan - 1;

                    // Ensure this merge exists in the workbook
                    try {
                        if (typeof sheet.range === 'function') {
                            const range = sheet.range(row, col, endRow, endCol);
                            if (typeof range.merged === 'function') {
                                // Set the merged state to true
                                range.merged(true);
                                console.log(`Successfully set merge in workbook at (${row},${col}) to (${endRow},${endCol})`);
                            }
                        }
                    } catch (rangeError) {
                        console.warn(`Error ensuring merge in workbook at (${row},${col}):`, rangeError);
                    }
                }
            } catch (cellError) {
                console.warn('Error processing merged cell:', cellError);
            }
        });

        // 2. Also process any merged cells defined in the workbook
        console.log('Processing any existing merge ranges defined in the workbook...');

        try {
            // Different workbook libraries have different ways to access merges
            // XlsxPopulate approach
            if (typeof sheet.mergedCells === 'function') {
                const mergedRanges = sheet.mergedCells();
                console.log(`Found ${mergedRanges.length} merged ranges in workbook`);

                // Ensure each merge is properly set
                mergedRanges.forEach(range => {
                    if (range && typeof range.merged === 'function') {
                        range.merged(true);
                    }
                });
            }
            // SheetJS approach
            else if (sheet.merges && Array.isArray(sheet.merges)) {
                console.log(`Found ${sheet.merges.length} merge definitions in SheetJS format`);
            }
        } catch (workbookMergeError) {
            console.warn('Error processing workbook merges:', workbookMergeError);
        }

        console.log('Merged cells synchronization completed');
    } catch (error) {
        console.error('Error ensuring merged cells persistence:', error);
        throw error; // Re-throw to allow caller to handle
    }
}

// Function to remove visual merge from the DOM
function removeVisualMerge(startRow, startCol, endRow, endCol) {
    try {
        console.log('Removing visual merges from DOM...');
        const table = document.getElementById('excelTable');
        if (!table) {
            console.warn('Excel table not found in DOM');
            return false;
        }

        let unmergeApplied = false;

        // Look for merged cells within the range
        for (let r = startRow; r <= endRow; r++) {
            for (let c = startCol; c <= endCol; c++) {
                // Check for a main merged cell
                const cell = table.querySelector(`td[data-row="${r}"][data-col="${c}"]`);

                if (cell && (cell.hasAttribute('rowspan') || cell.hasAttribute('colspan') ||
                    cell.hasAttribute('data-merged') || cell.classList.contains('merged-cell'))) {

                    console.log(`Found visually merged cell at (${r},${c})`);

                    // Get the rowspan and colspan
                    const rowSpan = parseInt(cell.getAttribute('rowspan') || '1');
                    const colSpan = parseInt(cell.getAttribute('colspan') || '1');

                    // Remove merge attributes
                    cell.removeAttribute('rowspan');
                    cell.removeAttribute('colspan');
                    cell.removeAttribute('data-merged');
                    cell.classList.remove('merged-cell');

                    // Find and unhide cells that were part of this merge
                    for (let mr = r; mr < r + rowSpan; mr++) {
                        for (let mc = c; mc < c + colSpan; mc++) {
                            // Skip the main cell
                            if (mr === r && mc === c) continue;

                            const mergedCell = table.querySelector(`td[data-row="${mr}"][data-col="${mc}"]`);
                            if (mergedCell) {
                                mergedCell.removeAttribute('data-merged-into');
                                mergedCell.style.display = '';
                            }
                        }
                    }

                    unmergeApplied = true;
                }

                // Check for cells that are part of a merge
                const hiddenCell = table.querySelector(`td[data-row="${r}"][data-col="${c}"][data-merged-into]`);
                if (hiddenCell) {
                    const [mergedRow, mergedCol] = hiddenCell.getAttribute('data-merged-into').split(',');
                    console.log(`Found hidden cell at (${r},${c}) merged into (${mergedRow},${mergedCol})`);

                    // Find the main merged cell
                    const mainCell = table.querySelector(`td[data-row="${mergedRow}"][data-col="${mergedCol}"]`);
                    if (mainCell) {
                        // Remove merge attributes from the main cell
                        mainCell.removeAttribute('rowspan');
                        mainCell.removeAttribute('colspan');
                        mainCell.removeAttribute('data-merged');
                        mainCell.classList.remove('merged-cell');
                    }

                    // Unhide this cell
                    hiddenCell.removeAttribute('data-merged-into');
                    hiddenCell.style.display = '';

                    unmergeApplied = true;
                }
            }
        }

        console.log(`Visual unmerge applied: ${unmergeApplied ? 'Yes' : 'No'}`);
        return unmergeApplied;
    } catch (error) {
        console.warn('Error removing visual merge:', error);
        return false;
    }
}
